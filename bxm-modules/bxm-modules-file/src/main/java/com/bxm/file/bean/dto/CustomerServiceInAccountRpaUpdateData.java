package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/30 29 21:50
 * happy coding!
 */
public class CustomerServiceInAccountRpaUpdateData implements EnterpriseData {
    @Excel(name = "客户名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "账期")
    private String period;

    @Getter
    @Setter
    @Excel(name = "RPA执行结果（成功/失败）")
    private String rpaExeResultStr;

    @Excel(name = "本年累计主营收入")
    private String majorIncomeTotal;

    @Excel(name = "本年累计主营成本")
    private String majorCostTotal;

    @Excel(name = "本年累计净利润")
    private String profitTotal;

    @Getter
    @Setter
    @Excel(name = "个税申报人数")
    private String taxReportCount;

    @Getter
    @Setter
    @Excel(name = "本年个税申报工资总额")
    private String taxReportSalaryTotal;

    @Getter
    @Setter
    @Excel(name = "报表是否平衡")
    private String tableStatusBalance;

    @Getter
    @Setter
    @Excel(name = "RPA查询时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date rpaSearchTime;

    @Getter
    @Setter
    @Excel(name = "RPA备注")
    private String rpaRemark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    @Getter
    @Setter
    private Integer index;

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }


    public String getMajorIncomeTotal() {
        return majorIncomeTotal;
    }

    public void setMajorIncomeTotal(String majorIncomeTotal) {
        this.majorIncomeTotal = majorIncomeTotal;
    }

    public String getMajorCostTotal() {
        return majorCostTotal;
    }

    public void setMajorCostTotal(String majorCostTotal) {
        this.majorCostTotal = majorCostTotal;
    }

    public String getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(String profitTotal) {
        this.profitTotal = profitTotal;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }
}
