package com.bxm.file.bean.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckResultData {

    @ApiModelProperty("类型")
    private String deliverType;

    @ApiModelProperty("总数据")
    private Long totalDataCount;

    @ApiModelProperty("正常数据")
    private Long successDataCount;

    @ApiModelProperty("正常无附件数据")
    private Long successNoFileDataCount;

    @ApiModelProperty("异常数据")
    private Long failDataCount;
}
