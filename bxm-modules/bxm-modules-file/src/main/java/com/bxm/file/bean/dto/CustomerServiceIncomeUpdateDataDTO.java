package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceIncomeUpdateDataDTO implements EnterpriseData {

    @Excel(name = "客户名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "账期")
    private String period;

    @Excel(name = "全量发票开票金额")
    private String allTicketAmount;

    @Excel(name = "全量发票开票税额")
    private String allTicketTaxAmount;

    @Excel(name = "全量取得发票金额")
    private String totalInvoiceAmount;

    @Excel(name = "全量取得发票税额")
    private String totalInvoiceTaxAmount;

    @Excel(name = "无票收入")
    private String noTicketIncomeAmount;

    @Excel(name = "开票取数截止时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date ticketTime;

    @Excel(name = "无票收入更新时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date noTicketIncomeTime;

    @Excel(name = "RPA最后取数时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date rpaTime;

    @Excel(name = "最后取数结果（成功、异常）")
    private String rpaResult;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getCheckError() {
        return checkError;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getAllTicketAmount() {
        return allTicketAmount;
    }

    public void setAllTicketAmount(String allTicketAmount) {
        this.allTicketAmount = allTicketAmount;
    }

    public String getAllTicketTaxAmount() {
        return allTicketTaxAmount;
    }

    public void setAllTicketTaxAmount(String allTicketTaxAmount) {
        this.allTicketTaxAmount = allTicketTaxAmount;
    }

    public String getTotalInvoiceAmount() {
        return totalInvoiceAmount;
    }

    public void setTotalInvoiceAmount(String totalInvoiceAmount) {
        this.totalInvoiceAmount = totalInvoiceAmount;
    }

    public String getTotalInvoiceTaxAmount() {
        return totalInvoiceTaxAmount;
    }

    public void setTotalInvoiceTaxAmount(String totalInvoiceTaxAmount) {
        this.totalInvoiceTaxAmount = totalInvoiceTaxAmount;
    }

    public String getNoTicketIncomeAmount() {
        return noTicketIncomeAmount;
    }

    public void setNoTicketIncomeAmount(String noTicketIncomeAmount) {
        this.noTicketIncomeAmount = noTicketIncomeAmount;
    }

    public Date getTicketTime() {
        return ticketTime;
    }

    public void setTicketTime(Date ticketTime) {
        this.ticketTime = ticketTime;
    }

    public Date getNoTicketIncomeTime() {
        return noTicketIncomeTime;
    }

    public void setNoTicketIncomeTime(Date noTicketIncomeTime) {
        this.noTicketIncomeTime = noTicketIncomeTime;
    }

    public Date getRpaTime() {
        return rpaTime;
    }

    public void setRpaTime(Date rpaTime) {
        this.rpaTime = rpaTime;
    }

    public String getRpaResult() {
        return rpaResult;
    }

    public void setRpaResult(String rpaResult) {
        this.rpaResult = rpaResult;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
