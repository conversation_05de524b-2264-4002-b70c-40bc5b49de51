package com.bxm.file;

import com.bxm.common.security.annotation.EnableCustomConfig;
import com.bxm.common.security.annotation.EnableJsFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.bxm.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 文件服务
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableJsFeignClients
@SpringBootApplication
@EnableAsync
public class BxmFileApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(BxmFileApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  文件服务模块启动成功   ");
    }
}
