package com.bxm.file.util;

import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;

import java.util.concurrent.Executors;

/**
 * 并发、多线程，工具类
 *
 * <AUTHOR>
 * @date 2024/8/23 16:45
 * happy coding!
 */
public class ListeningExecutorServiceUtil {
    //线程池大小
    private static final Integer POOL_SIZE = 20;

    //OSS并行获取数据的TIMEOUT时间
    public static final Integer FUTURE_GET_ACTIVITY_TIME_OUT = 2000;

    //线程池
    public static final ListeningExecutorService SERVICE = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(POOL_SIZE));
}
