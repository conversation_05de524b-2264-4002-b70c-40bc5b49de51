package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeCheckFileData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeCheckFileParser implements ExcelV2Parser<AccountingCashierIncomeCheckFileData> {

    @Override
    public List<AccountingCashierIncomeCheckFileData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeCheckFileData.class);
    }
}
