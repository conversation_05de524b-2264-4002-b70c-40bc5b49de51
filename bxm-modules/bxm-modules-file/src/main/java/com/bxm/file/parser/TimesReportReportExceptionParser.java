package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.TimesReportReportData;
import com.bxm.file.bean.dto.batchDeliverV2.TimesReportReportExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class TimesReportReportExceptionParser implements ExcelV2Parser<TimesReportReportExceptionData> {

    @Override
    public List<TimesReportReportExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, TimesReportReportExceptionData.class);
    }
}
