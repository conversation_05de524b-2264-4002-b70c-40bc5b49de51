package com.bxm.file.util;

import com.bxm.common.core.utils.StringUtils;
import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import net.sf.sevenzipjbinding.ArchiveFormat;
import net.sf.sevenzipjbinding.IInArchive;
import net.sf.sevenzipjbinding.PropID;
import net.sf.sevenzipjbinding.SevenZip;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;
import org.apache.commons.compress.archivers.ArchiveInputStream;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class ArchiveUtils {

    public static boolean validateArchiveFile(InputStream inputStream, String fileName) throws Exception {
        if (fileName.endsWith(".zip")) {
            return validateZipFile(inputStream);
        } else if (fileName.endsWith(".rar")) {
            return validateRarFile(inputStream);
        }
        return false;
    }

    private static boolean validateZipFile(InputStream inputStream) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(inputStream)) {
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                if (entry.isDirectory()) {
                    return true;
                }
                entry = zis.getNextEntry();
            }
        }
        return false;
    }

    private static boolean validateRarFile(InputStream inputStream) throws Exception {
        try (Archive archive = new Archive(new ByteArrayInputStream(toByteArray(inputStream)))) {
            FileHeader fileHeader = archive.nextFileHeader();
            while (fileHeader != null) {
                if (fileHeader.isDirectory()) {
                    return true;
                }
                fileHeader = archive.nextFileHeader();
            }
        }
        return false;
    }

    public static long countArchiveFiles(InputStream inputStream, Boolean isZip) throws Exception {
        if (isZip) {
            return countZipFiles(inputStream);
        } else {
            return countRarFiles(inputStream);
        }
    }

    public static long countArchiveFilesV2(InputStream inputStream, Boolean isZip) throws Exception {
        if (isZip) {
            return countZipFilesV2(inputStream);
        } else {
            return countRarFilesV2(inputStream);
        }
    }

    private static long countZipFiles(InputStream inputStream) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(inputStream)) {
            ZipEntry entry;
            long fileCount = 0;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    String fileName = entry.getName();
                    String creditCode = extractCreditCodeFromPath(fileName);
                    if (StringUtils.isEmpty(creditCode)) {
                        continue;
                    }
                    fileCount++;
                }
            }
            return fileCount;
        }
    }

    private static long countZipFilesV2(InputStream inputStream) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(inputStream)) {
            ZipEntry entry;
            long fileCount = 0;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    fileCount++;
                }
            }
            return fileCount;
        }
    }

    private static long countRarFiles(InputStream inputStream) throws Exception {
        File tempFile = File.createTempFile("temp", "rar");
        try (FileOutputStream out = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        }

        long fileCount = 0;
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(tempFile, "r");
             IInArchive inArchive = SevenZip.openInArchive(null, new RandomAccessFileInStream(randomAccessFile))) {
            if (inArchive != null) {
                for (int i = 0; i < inArchive.getNumberOfItems(); i++) {
                    if (!(Boolean) inArchive.getProperty(i, PropID.IS_FOLDER)) {
                        String entryFileName = (String) inArchive.getProperty(i, PropID.PATH);
                        String creditCode = extractCreditCodeFromPath(entryFileName);
                        if (StringUtils.isEmpty(creditCode)) {
                            continue;
                        }
                        fileCount++;
                    }
                }
            }
        } catch (Exception e) {
            fileCount = 0;
        } finally {
            tempFile.delete();
        }
        return fileCount;
    }

    private static long countRarFilesV2(InputStream inputStream) throws Exception {
        File tempFile = File.createTempFile("temp", "rar");
        try (FileOutputStream out = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        }

        long fileCount = 0;
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(tempFile, "r");
             IInArchive inArchive = SevenZip.openInArchive(null, new RandomAccessFileInStream(randomAccessFile))) {
            if (inArchive != null) {
                for (int i = 0; i < inArchive.getNumberOfItems(); i++) {
                    if (!(Boolean) inArchive.getProperty(i, PropID.IS_FOLDER)) {
                        fileCount++;
                    }
                }
            }
        } catch (Exception e) {
            fileCount = 0;
        } finally {
            tempFile.delete();
        }
        return fileCount;
    }

    private static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int n;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    private static String extractCreditCodeFromPath(String filePath) {
        if (filePath.contains("/") && filePath.split("/").length == 2) {
            String pathName = filePath.split("/")[0];
            if (pathName.contains("-") && pathName.split("-").length == 3) {
                return pathName.split("-")[1];
            }
            if (pathName.contains("_") && pathName.split("_").length == 3) {
                return pathName.split("_")[1];
            }
        }
        return "";
    }
}
