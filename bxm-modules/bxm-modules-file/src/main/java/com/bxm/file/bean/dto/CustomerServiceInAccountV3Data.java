package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 21:30
 * happy coding!
 */
public class CustomerServiceInAccountV3Data implements EnterpriseData {

    @Excel(name = "客户名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "账期")
    private String period;

//    @Getter
//    @Setter
//    @Excel(name = "交付结果（正常/无需交付）")
//    private String deliverResultStr;
//
//    @Excel(name = "银行流水录入时间", dateFormat = "yyyy/M/d")
//    private Date bankInTime;
//
//    @Excel(name = "入账时间", dateFormat = "yyyy/M/d")
//    private Date inTime;

    @Getter
    @Setter
    @Excel(name = "银行流水录入结果（正常、异常、无需交付、已开户无流水、未开户、银行部分缺）")
    private String bankPaymentInputResultStr;

    @Getter
    @Setter
    @Excel(name = "入账结果（正常、异常、无需交付、无账务）")
    private String inAccountResultStr;

    @Excel(name = "本年累计主营收入")
    private String majorIncomeTotal;

    @Excel(name = "本年累计主营成本")
    private String majorCostTotal;

    @Excel(name = "本年累计净利润")
    private String profitTotal;

    @Getter
    @Setter
    @Excel(name = "个税申报人数")
    private String taxReportCount;

    @Getter
    @Setter
    @Excel(name = "本年个税申报工资总额")
    private String taxReportSalaryTotal;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    @Getter
    @Setter
    private Integer index;

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getMajorIncomeTotal() {
        return majorIncomeTotal;
    }

    public void setMajorIncomeTotal(String majorIncomeTotal) {
        this.majorIncomeTotal = majorIncomeTotal;
    }

    public String getMajorCostTotal() {
        return majorCostTotal;
    }

    public void setMajorCostTotal(String majorCostTotal) {
        this.majorCostTotal = majorCostTotal;
    }

    public String getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(String profitTotal) {
        this.profitTotal = profitTotal;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }
}