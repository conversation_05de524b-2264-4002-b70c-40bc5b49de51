package com.bxm.file.util;

import com.github.junrar.Archive;
import com.github.junrar.exception.RarException;
import com.github.junrar.rarfile.FileHeader;

import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class RarUtils {

    public static long countRarFiles(InputStream inputStream) throws Exception {
        Archive archive = new Archive(inputStream);
        FileHeader fileHeader;
        long fileCount = 0;
        while ((fileHeader = archive.nextFileHeader()) != null) {
            if (!fileHeader.isDirectory()) {
                fileCount++;
            }
        }
        return fileCount;
    }
}
