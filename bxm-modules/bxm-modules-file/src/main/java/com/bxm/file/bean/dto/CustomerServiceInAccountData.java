package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

public class CustomerServiceInAccountData implements EnterpriseData {

    @Excel(name = "客户名")
    private String enterpriseName;

    @Excel(name = "税号")
    private String creditCode;

    @Excel(name = "账期")
    private String period;

    @Excel(name = "入账时间", dateFormat = "yyyy/M/d")
    private Date inTime;

    @Excel(name = "银行流水录入时间", dateFormat = "yyyy/M/d")
    private Date bankInTime;

    @Excel(name = "主营收入累计")
    private String majorIncomeTotal;

    @Excel(name = "主营成本累计")
    private String majorCostTotal;

    @Excel(name = "利润累计")
    private String profitTotal;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    @Getter
    @Setter
    private Integer index;

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Date getInTime() {
        return inTime;
    }

    public void setInTime(Date inTime) {
        this.inTime = inTime;
    }

    public Date getBankInTime() {
        return bankInTime;
    }

    public void setBankInTime(Date bankInTime) {
        this.bankInTime = bankInTime;
    }

    public String getMajorIncomeTotal() {
        return majorIncomeTotal;
    }

    public void setMajorIncomeTotal(String majorIncomeTotal) {
        this.majorIncomeTotal = majorIncomeTotal;
    }

    public String getMajorCostTotal() {
        return majorCostTotal;
    }

    public void setMajorCostTotal(String majorCostTotal) {
        this.majorCostTotal = majorCostTotal;
    }

    public String getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(String profitTotal) {
        this.profitTotal = profitTotal;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }
}
