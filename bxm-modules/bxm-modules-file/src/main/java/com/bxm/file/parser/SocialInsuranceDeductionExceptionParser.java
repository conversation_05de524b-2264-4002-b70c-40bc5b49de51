package com.bxm.file.parser;

import com.bxm.file.bean.dto.SocialInsuranceDeductionExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SocialInsuranceDeductionExceptionParser implements ExcelParser<SocialInsuranceDeductionExceptionData>{
    @Override
    public List<SocialInsuranceDeductionExceptionData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析社保的 Excel 文件
        return ExcelUtils.parseExcelFile(file, SocialInsuranceDeductionExceptionData.class);
    }
}
