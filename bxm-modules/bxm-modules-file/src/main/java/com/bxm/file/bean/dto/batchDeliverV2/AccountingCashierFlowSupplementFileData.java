package com.bxm.file.bean.dto.batchDeliverV2;

import com.bxm.common.core.annotation.Excel;
import com.bxm.file.bean.dto.AliFileDTO;

import java.util.List;

public class AccountingCashierFlowSupplementFileData implements EnterpriseV2Data {

    @Excel(name = "企业名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "银行账号")
    private String bankAccountNumber;

    @Excel(name = "账期：yyyymm")
    private String period;

    @Excel(name = "对账单补充方式：追加、覆盖、无需补充")
    private String coverFileType;

    @Excel(name = "对账单文件名")
    private String materialFileName;

    @Excel(name = "对账单文件夹名")
    private String materialDirectName;

    @Excel(name = "回单补充方式：追加、覆盖、无需补充")
    private String receiptCoverFileType;

    @Excel(name = "回单文件名")
    private String receiptMaterialFileName;

    @Excel(name = "回单文件夹名")
    private String receiptMaterialDirectName;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> medicalFiles;

    private List<AliFileDTO> socialFiles;

    private Long customerServiceId;

    private Long periodId;

    private Long medicalDeliverId;

    private Long socialDeliverId;

    private Boolean doMedical;

    private Boolean doSocial;

    private Boolean isMedical;

    private Boolean isSocial;

    private String medicalCheckError;

    private String socialCheckError;

    private AliFileDTO checkFile;

    private List<AliFileDTO> checkFiles;

    private AliFileDTO receiptFile;

    private List<AliFileDTO> receiptFiles;

    public AliFileDTO getCheckFile() {
        return checkFile;
    }

    public void setCheckFile(AliFileDTO checkFile) {
        this.checkFile = checkFile;
    }

    public List<AliFileDTO> getCheckFiles() {
        return checkFiles;
    }

    public void setCheckFiles(List<AliFileDTO> checkFiles) {
        this.checkFiles = checkFiles;
    }

    public AliFileDTO getReceiptFile() {
        return receiptFile;
    }

    public void setReceiptFile(AliFileDTO receiptFile) {
        this.receiptFile = receiptFile;
    }

    public List<AliFileDTO> getReceiptFiles() {
        return receiptFiles;
    }

    public void setReceiptFiles(List<AliFileDTO> receiptFiles) {
        this.receiptFiles = receiptFiles;
    }

    public String getCoverFileType() {
        return coverFileType;
    }

    public void setCoverFileType(String coverFileType) {
        this.coverFileType = coverFileType;
    }

    @Override
    public String getMedicalCheckError() {
        return medicalCheckError;
    }

    @Override
    public String getSocialCheckError() {
        return socialCheckError;
    }

    @Override
    public boolean hasMedicalErrors() {
        return medicalCheckError != null && !medicalCheckError.isEmpty();
    }

    @Override
    public void addMedicalCheckError(String checkError) {
        if (this.medicalCheckError == null || this.medicalCheckError.isEmpty()) {
            this.medicalCheckError = checkError;
        } else {
            this.medicalCheckError += "; " + checkError;
        }
    }

    @Override
    public boolean hasSocialErrors() {
        return socialCheckError != null && !socialCheckError.isEmpty();
    }

    @Override
    public void addSocialCheckError(String checkError) {
        if (this.socialCheckError == null || this.socialCheckError.isEmpty()) {
            this.socialCheckError = checkError;
        } else {
            this.socialCheckError += "; " + checkError;
        }
    }

    @Override
    public Boolean getIsMedical() {
        return isMedical;
    }

    @Override
    public void setIsMedical(Boolean medical) {
        isMedical = medical;
    }

    @Override
    public Boolean getIsSocial() {
        return isSocial;
    }

    @Override
    public void setIsSocial(Boolean social) {
        isSocial = social;
    }

    @Override
    public Boolean getDoMedical() {
        return doMedical;
    }

    @Override
    public void setDoMedical(Boolean doMedical) {
        this.doMedical = doMedical;
    }

    @Override
    public Boolean getDoSocial() {
        return doSocial;
    }

    @Override
    public void setDoSocial(Boolean doSocial) {
        this.doSocial = doSocial;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public List<AliFileDTO> getMedicalFiles() {
        return medicalFiles;
    }

    public void setMedicalFiles(List<AliFileDTO> medicalFiles) {
        this.medicalFiles = medicalFiles;
    }

    @Override
    public List<AliFileDTO> getSocialFiles() {
        return socialFiles;
    }

    public void setSocialFiles(List<AliFileDTO> socialFiles) {
        this.socialFiles = socialFiles;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    @Override
    public Long getMedicalDeliverId() {
        return medicalDeliverId;
    }

    public void setMedicalDeliverId(Long medicalDeliverId) {
        this.medicalDeliverId = medicalDeliverId;
    }

    @Override
    public Long getSocialDeliverId() {
        return socialDeliverId;
    }

    public void setSocialDeliverId(Long socialDeliverId) {
        this.socialDeliverId = socialDeliverId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getMaterialDirectName() {
        return materialDirectName;
    }

    public void setMaterialDirectName(String materialDirectName) {
        this.materialDirectName = materialDirectName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBankAccountNumber() {
        return bankAccountNumber;
    }

    public void setBankAccountNumber(String bankAccountNumber) {
        this.bankAccountNumber = bankAccountNumber;
    }

    public String getMaterialFileName() {
        return materialFileName;
    }

    public void setMaterialFileName(String materialFileName) {
        this.materialFileName = materialFileName;
    }

    public String getReceiptCoverFileType() {
        return receiptCoverFileType;
    }

    public void setReceiptCoverFileType(String receiptCoverFileType) {
        this.receiptCoverFileType = receiptCoverFileType;
    }

    public String getReceiptMaterialFileName() {
        return receiptMaterialFileName;
    }

    public void setReceiptMaterialFileName(String receiptMaterialFileName) {
        this.receiptMaterialFileName = receiptMaterialFileName;
    }

    public String getReceiptMaterialDirectName() {
        return receiptMaterialDirectName;
    }

    public void setReceiptMaterialDirectName(String receiptMaterialDirectName) {
        this.receiptMaterialDirectName = receiptMaterialDirectName;
    }
}
