package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierFlowCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierFlowCreateParser implements ExcelV2Parser<AccountingCashierFlowCreateData> {

    @Override
    public List<AccountingCashierFlowCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierFlowCreateData.class);
    }
}
