package com.bxm.file.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.file.domain.CustomerRpaDetailFile;

/**
 * 交付明细附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface CustomerRpaDetailFileMapper extends BaseMapper<CustomerRpaDetailFile>
{
    /**
     * 查询交付明细附件
     * 
     * @param id 交付明细附件主键
     * @return 交付明细附件
     */
    public CustomerRpaDetailFile selectCustomerRpaDetailFileById(Long id);

    /**
     * 查询交付明细附件列表
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 交付明细附件集合
     */
    public List<CustomerRpaDetailFile> selectCustomerRpaDetailFileList(CustomerRpaDetailFile customerRpaDetailFile);

    /**
     * 新增交付明细附件
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 结果
     */
    public int insertCustomerRpaDetailFile(CustomerRpaDetailFile customerRpaDetailFile);

    /**
     * 修改交付明细附件
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 结果
     */
    public int updateCustomerRpaDetailFile(CustomerRpaDetailFile customerRpaDetailFile);

    /**
     * 删除交付明细附件
     * 
     * @param id 交付明细附件主键
     * @return 结果
     */
    public int deleteCustomerRpaDetailFileById(Long id);

    /**
     * 批量删除交付明细附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerRpaDetailFileByIds(Long[] ids);
}
