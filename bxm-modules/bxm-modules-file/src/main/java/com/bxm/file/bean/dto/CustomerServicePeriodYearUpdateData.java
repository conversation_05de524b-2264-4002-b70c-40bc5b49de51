package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;

import java.util.List;

public class CustomerServicePeriodYearUpdateData implements EnterpriseData {

    @Excel(name = "客户名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "上年可弥补损益")
    private String lastYearDeductible;

    @Excel(name = "上年暂估未冲")
    private String priorYearEstimationNotReversed;

    @Excel(name = "上年折旧调整")
    private String priorYearDepreciationAdjustment;

    @Excel(name = "全年结账（待完成、已完成）")
    private String fullYearClosing;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getCheckError() {
        return checkError;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public String getLastYearDeductible() {
        return lastYearDeductible;
    }

    public void setLastYearDeductible(String lastYearDeductible) {
        this.lastYearDeductible = lastYearDeductible;
    }

    public String getPriorYearEstimationNotReversed() {
        return priorYearEstimationNotReversed;
    }

    public void setPriorYearEstimationNotReversed(String priorYearEstimationNotReversed) {
        this.priorYearEstimationNotReversed = priorYearEstimationNotReversed;
    }

    public String getPriorYearDepreciationAdjustment() {
        return priorYearDepreciationAdjustment;
    }

    public void setPriorYearDepreciationAdjustment(String priorYearDepreciationAdjustment) {
        this.priorYearDepreciationAdjustment = priorYearDepreciationAdjustment;
    }

    public String getFullYearClosing() {
        return fullYearClosing;
    }

    public void setFullYearClosing(String fullYearClosing) {
        this.fullYearClosing = fullYearClosing;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
