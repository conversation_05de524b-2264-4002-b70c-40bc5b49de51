package com.bxm.file.parser;

import com.bxm.file.bean.dto.CustomerServiceInAccountRpaUpdateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 21:55
 * happy coding!
 */
@Component
public class CustomerServiceInAccountRpaUpdateParser implements ExcelParser<CustomerServiceInAccountRpaUpdateData> {
    @Override
    public List<CustomerServiceInAccountRpaUpdateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerServiceInAccountRpaUpdateData.class);
    }
}
