package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.SettleAnnualSupplementReportFileData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SettleAnnualSupplementReportFilesParser implements ExcelV2Parser<SettleAnnualSupplementReportFileData> {

    @Override
    public List<SettleAnnualSupplementReportFileData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, SettleAnnualSupplementReportFileData.class);
    }
}
