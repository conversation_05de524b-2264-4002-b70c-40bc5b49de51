package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.MaterialDeliverTicketInAccountCreateV2Data;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class MaterialDeliverTicketInAccountCreateV2Parser implements ExcelV2Parser<MaterialDeliverTicketInAccountCreateV2Data> {

    @Override
    public List<MaterialDeliverTicketInAccountCreateV2Data> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, MaterialDeliverTicketInAccountCreateV2Data.class);
    }
}
