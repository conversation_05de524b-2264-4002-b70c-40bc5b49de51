package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.MaterialDeliverNormalInAccountCreateV2Data;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class MaterialDeliverNormalInAccountCreateV2Parser implements ExcelV2Parser<MaterialDeliverNormalInAccountCreateV2Data> {

    @Override
    public List<MaterialDeliverNormalInAccountCreateV2Data> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, MaterialDeliverNormalInAccountCreateV2Data.class);
    }
}
