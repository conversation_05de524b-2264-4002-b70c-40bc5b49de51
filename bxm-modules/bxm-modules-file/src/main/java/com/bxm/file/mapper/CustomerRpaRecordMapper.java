package com.bxm.file.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.file.domain.CustomerRpaRecord;

/**
 * rap交付记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface CustomerRpaRecordMapper extends BaseMapper<CustomerRpaRecord>
{
    /**
     * 查询rap交付记录
     * 
     * @param id rap交付记录主键
     * @return rap交付记录
     */
    public CustomerRpaRecord selectCustomerRpaRecordById(Long id);

    /**
     * 查询rap交付记录列表
     * 
     * @param customerRpaRecord rap交付记录
     * @return rap交付记录集合
     */
    public List<CustomerRpaRecord> selectCustomerRpaRecordList(CustomerRpaRecord customerRpaRecord);

    /**
     * 新增rap交付记录
     * 
     * @param customerRpaRecord rap交付记录
     * @return 结果
     */
    public int insertCustomerRpaRecord(CustomerRpaRecord customerRpaRecord);

    /**
     * 修改rap交付记录
     * 
     * @param customerRpaRecord rap交付记录
     * @return 结果
     */
    public int updateCustomerRpaRecord(CustomerRpaRecord customerRpaRecord);

    /**
     * 删除rap交付记录
     * 
     * @param id rap交付记录主键
     * @return 结果
     */
    public int deleteCustomerRpaRecordById(Long id);

    /**
     * 批量删除rap交付记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerRpaRecordByIds(Long[] ids);
}
