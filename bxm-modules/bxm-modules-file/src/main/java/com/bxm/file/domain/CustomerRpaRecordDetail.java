package com.bxm.file.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * rap明细对象 c_customer_rpa_record_detail
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@ApiModel("rap明细对象")
@Accessors(chain = true)
@TableName("c_customer_rpa_record_detail")
public class CustomerRpaRecordDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /** rpa记录id */
    @Excel(name = "rpa记录id")
    @TableField("rpa_record_id")
    @ApiModelProperty(value = "rpa记录id")
    private Long rpaRecordId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 状态，0-待确认，1-处理中，2-成功，3-失败 */
    @Excel(name = "状态，0-待确认，1-处理中，2-成功，3-失败")
    @TableField("status")
    @ApiModelProperty(value = "状态，0-待确认，1-处理中，2-成功，3-失败")
    private Integer status;

    /** 失败原因 */
    @Excel(name = "失败原因")
    @TableField("fail_reason")
    @ApiModelProperty(value = "失败原因")
    private String failReason;

    /** rpa执行结果 */
    @Excel(name = "rpa执行结果")
    @TableField("rpa_result")
    @ApiModelProperty(value = "rpa执行结果")
    private String rpaResult;

    /** 申报结果 */
    @Excel(name = "申报结果")
    @TableField("report_result")
    @ApiModelProperty(value = "申报结果")
    private String reportResult;

    /** 异常信息 */
    @Excel(name = "异常信息")
    @TableField("exception_msg")
    @ApiModelProperty(value = "异常信息")
    private String exceptionMsg;

    /** 数据内容 */
    @Excel(name = "数据内容")
    @TableField("content")
    @ApiModelProperty(value = "数据内容")
    private String content;

}
