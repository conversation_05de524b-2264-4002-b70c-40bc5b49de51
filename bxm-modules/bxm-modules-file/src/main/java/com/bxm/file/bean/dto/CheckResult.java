package com.bxm.file.bean.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class CheckResult {

    @ApiModelProperty("总文件数量")
    private Long totalFileCount;

    @ApiModelProperty("已完成文件数量")
    private Long completeFileCount;

    @ApiModelProperty("解析数据")
    private List<? extends EnterpriseData> dataList;

    @ApiModelProperty("是否有异常数据")
    private Boolean hasException;

    @ApiModelProperty("是否解析完成")
    private Boolean isComplete;

    @ApiModelProperty("异常信息")
    private List<String> errorStatistics;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @ApiModelProperty("批次号")
    private String batchNo;

    private Integer operType;

    private Integer deliverType;

    private Integer period;

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(Integer deliverType) {
        this.deliverType = deliverType;
    }

    public Integer getOperType() {
        return operType;
    }

    public void setOperType(Integer operType) {
        this.operType = operType;
    }

    public CheckResult() {

    }

    public CheckResult(Long totalFileCount, Long completeFileCount, List<? extends EnterpriseData> dataList, Boolean hasException, Boolean isComplete, List<String> errorStatistics, String batchNo, Integer operType, Integer deliverType, Integer period) {
        this.totalFileCount = totalFileCount;
        this.completeFileCount = completeFileCount;
        this.dataList = dataList;
        this.hasException = hasException;
        this.isComplete = isComplete;
        this.errorStatistics = errorStatistics;
        this.batchNo = batchNo;
        this.operType = operType;
        this.deliverType = deliverType;
        this.period = period;
    }

    // Getters and Setters

    public Long getTotalFileCount() {
        return totalFileCount;
    }

    public void setTotalFileCount(Long totalFileCount) {
        this.totalFileCount = totalFileCount;
    }

    public Long getCompleteFileCount() {
        return completeFileCount;
    }

    public void setCompleteFileCount(Long completeFileCount) {
        this.completeFileCount = completeFileCount;
    }

    public List<? extends EnterpriseData> getDataList() {
        return dataList;
    }

    public void setDataList(List<? extends EnterpriseData> dataList) {
        this.dataList = dataList;
    }

    public Boolean getHasException() {
        return hasException;
    }

    public void setHasException(Boolean hasException) {
        this.hasException = hasException;
    }

    public Boolean getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Boolean isComplete) {
        this.isComplete = isComplete;
    }

    public List<String> getErrorStatistics() {
        return errorStatistics;
    }

    public void setErrorStatistics(List<String> errorStatistics) {
        this.errorStatistics = errorStatistics;
    }
}
