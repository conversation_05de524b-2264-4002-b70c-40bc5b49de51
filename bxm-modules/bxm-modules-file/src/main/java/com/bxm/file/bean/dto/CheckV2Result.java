package com.bxm.file.bean.dto;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.file.bean.dto.batchDeliverV2.EnterpriseV2Data;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class CheckV2Result {

    @ApiModelProperty("总文件数量")
    private Long totalFileCount;

    @ApiModelProperty("已完成文件数量")
    private Long completeFileCount;

    @ApiModelProperty("是否有异常数据")
    private Boolean hasException;

    @ApiModelProperty("是否解析完成")
    private Boolean isComplete;

    @ApiModelProperty("解析结果")
    private List<CheckResultData> checkResultDataList;

    @ApiModelProperty("解析数据")
    private List<? extends EnterpriseV2Data> dataList;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @ApiModelProperty("批次号")
    private String batchNo;

    private Integer operType;

    private Integer deliverType;

    private Integer period;

    public Long getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(Long adminUserId) {
        this.adminUserId = adminUserId;
    }

    private Long adminUserId;

    private Long commitDeptId;

    private CommonFileVO exclFile;

    private CommonFileVO zipFile;

    private CommonFileVO errorFile;

    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public CommonFileVO getExclFile() {
        return exclFile;
    }

    public void setExclFile(CommonFileVO exclFile) {
        this.exclFile = exclFile;
    }

    public CommonFileVO getZipFile() {
        return zipFile;
    }

    public void setZipFile(CommonFileVO zipFile) {
        this.zipFile = zipFile;
    }

    public CommonFileVO getErrorFile() {
        return errorFile;
    }

    public void setErrorFile(CommonFileVO errorFile) {
        this.errorFile = errorFile;
    }

    public Long getCommitDeptId() {
        return commitDeptId;
    }

    public void setCommitDeptId(Long commitDeptId) {
        this.commitDeptId = commitDeptId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(Integer deliverType) {
        this.deliverType = deliverType;
    }

    public Integer getOperType() {
        return operType;
    }

    public void setOperType(Integer operType) {
        this.operType = operType;
    }

    public CheckV2Result() {

    }

    public CheckV2Result(Long totalFileCount, Long completeFileCount, Boolean hasException, Boolean isComplete, List<CheckResultData> checkResultDataList, List<? extends EnterpriseV2Data> dataList, String batchNo, Integer operType, Integer deliverType, Integer period) {
        this.totalFileCount = totalFileCount;
        this.completeFileCount = completeFileCount;
        this.hasException = hasException;
        this.isComplete = isComplete;
        this.checkResultDataList = checkResultDataList;
        this.dataList = dataList;
        this.batchNo = batchNo;
        this.operType = operType;
        this.deliverType = deliverType;
        this.period = period;
    }

    // Getters and Setters

    public Long getTotalFileCount() {
        return totalFileCount;
    }

    public void setTotalFileCount(Long totalFileCount) {
        this.totalFileCount = totalFileCount;
    }

    public Long getCompleteFileCount() {
        return completeFileCount;
    }

    public void setCompleteFileCount(Long completeFileCount) {
        this.completeFileCount = completeFileCount;
    }

    public Boolean getHasException() {
        return hasException;
    }

    public void setHasException(Boolean hasException) {
        this.hasException = hasException;
    }

    public Boolean getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Boolean isComplete) {
        this.isComplete = isComplete;
    }

    public List<CheckResultData> getCheckResultDataList() {
        return checkResultDataList;
    }

    public void setCheckResultDataList(List<CheckResultData> checkResultDataList) {
        this.checkResultDataList = checkResultDataList;
    }

    public List<? extends EnterpriseV2Data> getDataList() {
        return dataList;
    }

    public void setDataList(List<? extends EnterpriseV2Data> dataList) {
        this.dataList = dataList;
    }
}
