package com.bxm.file.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * rap交付记录对象 c_customer_rpa_record
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@ApiModel("rap交付记录对象")
@Accessors(chain = true)
@TableName("c_customer_rpa_record")
public class CustomerRpaRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /** rpa类型，1-医社保，2-个税（工资薪金），3-国税，4-收入，5-预认证 */
    @Excel(name = "rpa类型，1-医社保，2-个税（工资薪金），3-国税，4-收入，5-预认证")
    @TableField("rpa_type")
    @ApiModelProperty(value = "rpa类型，1-医社保，2-个税（工资薪金），3-国税，4-收入，5-预认证")
    private Integer rpaType;

    /** 操作类型，1-申报，2-扣款 */
    @Excel(name = "操作类型，1-申报，2-扣款")
    @TableField("oper_type")
    @ApiModelProperty(value = "操作类型，1-申报，2-扣款")
    private Integer operType;

    /** 批次号 */
    @Excel(name = "批次号")
    @TableField("batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 申请组织id */
    @Excel(name = "申请组织id")
    @TableField("dept_id")
    @ApiModelProperty(value = "申请组织id")
    private Long deptId;

    /** 数据文件地址 */
    @Excel(name = "数据文件地址")
    @TableField("data_file_url")
    @ApiModelProperty(value = "数据文件地址")
    private String dataFileUrl;

    /** 附件文件址 */
    @Excel(name = "附件文件址")
    @TableField("attach_file_url")
    @ApiModelProperty(value = "附件文件址")
    private String attachFileUrl;

    /** 状态，0-处理中，1-处理完成待确认，2-已确认，3-已退回 */
    @Excel(name = "状态，0-处理中，1-处理完成待确认，2-已确认，3-已退回")
    @TableField("status")
    @ApiModelProperty(value = "状态，0-处理中，1-处理完成待确认，2-已确认，3-已退回")
    private Integer status;

    /** 是否有错误数据，0-否，1-是 */
    @Excel(name = "是否有错误数据，0-否，1-是")
    @TableField("has_error_data")
    @ApiModelProperty(value = "是否有错误数据，0-否，1-是")
    private Boolean hasErrorData;

    /** 总数据量 */
    @Excel(name = "总数据量")
    @TableField("total_data_count")
    @ApiModelProperty(value = "总数据量")
    private Long totalDataCount;

    /** 可导入数据量 */
    @Excel(name = "可导入数据量")
    @TableField("import_data_count")
    @ApiModelProperty(value = "可导入数据量")
    private Long importDataCount;

    /** 确认操作备注 */
    @Excel(name = "确认操作备注")
    @TableField("confirm_remark")
    @ApiModelProperty(value = "确认操作备注")
    private String confirmRemark;

    /** 确认操作附件地址，多个逗号隔开 */
    @Excel(name = "确认操作附件地址，多个逗号隔开")
    @TableField("confirm_files")
    @ApiModelProperty(value = "确认操作附件地址，多个逗号隔开")
    private String confirmFiles;
}
