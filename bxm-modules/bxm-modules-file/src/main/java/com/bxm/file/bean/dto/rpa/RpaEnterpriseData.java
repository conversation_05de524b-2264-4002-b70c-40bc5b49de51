package com.bxm.file.bean.dto.rpa;

import com.bxm.file.bean.dto.AliFileDTO;

import java.util.List;

public interface RpaEnterpriseData {

    boolean hasErrors();

    void setCheckError(String checkError);

    void addCheckError(String checkError);

    String getCheckError();

    List<AliFileDTO> getFiles();

    String getSheetIndex();

    String getCreditCode();

    void setCreditCode(String creditCode);
}
