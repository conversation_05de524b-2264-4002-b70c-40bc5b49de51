package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class PreAuthAuthData implements EnterpriseData {

    @Excel(name = "企业名称")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "认证结果：成功/异常")
    private String authResult;

    @ApiModelProperty("上期留抵税额")
    @Excel(name = "上期留抵税额")
    private String lastPeriodCarriedForwardTax;

    @ApiModelProperty("本期已认证进项金额")
    @Excel(name = "本期已认证进项金额")
    private String currentCertifiedInputAmount;

    @ApiModelProperty("本期待确认进项金额")
    @Excel(name = "本期待确认进项金额")
    private String currentPendingInputAmount;

    @ApiModelProperty("本月(季)销售金额")
    @Excel(name = "本月(季)销售金额")
    private String currentMonthOrQuarterSalesAmount;

    @ApiModelProperty("无票收入")
    @Excel(name = "无票收入")
    private String unInvoicedIncome;

    @ApiModelProperty("本期销项税额")
    @Excel(name = "本期销项税额")
    private String currentOutputTax;

    @ApiModelProperty("本期简易征收税额")
    @Excel(name = "本期简易征收税额")
    private String currentSimpleTaxCollection;

    @ApiModelProperty("本期已认证进项税额")
    @Excel(name = "本期已认证进项税额")
    private String currentCertifiedInputTax;

    @ApiModelProperty("本期待确认进项税额")
    @Excel(name = "本期待确认进项税额")
    private String currentPendingInputTax;

    @ApiModelProperty("本期已勾选进项税额")
    @Excel(name = "本期已勾选进项税额")
    private String currentSelectedInputTax;

    @ApiModelProperty("用于计税的待确认进项范围")
    @Excel(name = "用于计税的待确认进项范围")
    private String pendingInputTaxForCalculation;

    @ApiModelProperty("本期已勾选并确认的进项税额")
    @Excel(name = "本期已勾选并确认的进项税额")
    private String currentSelectedAndCertifiedInputTax;

    @ApiModelProperty("本期红字进项应转出税额")
    @Excel(name = "本期红字进项应转出税额")
    private String currentRedLetterInputTaxShouldRevert;

    @ApiModelProperty("本期预计应缴增值税")
    @Excel(name = "本期预计应缴增值税")
    private String currentExpectedPayableVat;

    @ApiModelProperty("本期已缴增值税")
    @Excel(name = "本期已缴增值税")
    private String currentPaidVat;

    @ApiModelProperty("本期实际申报认证进项税额")
    @Excel(name = "本期实际申报认证进项税额")
    private String currentActualReportedInputTax;

    @ApiModelProperty("本期预计结余可用进项税额")
    @Excel(name = "本期预计结余可用进项税额")
    private String currentExpectedRemainingInputTax;

    @ApiModelProperty("本期开票张数")
    @Excel(name = "本期开票张数")
    private String currentInvoiceCount;

    @ApiModelProperty("上年增值税税负率")
    @Excel(name = "上年增值税税负率")
    private String lastYearVatBurdenRate;

    @ApiModelProperty("截止上期本年增值税税负率")
    @Excel(name = "截止上期本年增值税税负率")
    private String cumulativeVatBurdenRateToLastPeriod;

    @ApiModelProperty("截止上期本年累计缴纳增值税")
    @Excel(name = "截止上期本年累计缴纳增值税")
    private String cumulativeVatPaidToLastPeriod;

    @ApiModelProperty("截止上月本年累计加速加计扣除进项税额")
    @Excel(name = "截止上月本年累计加速加计扣除进项税额")
    private String cumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth;

    @ApiModelProperty("截止上期本年累计营业额")
    @Excel(name = "截止上期本年累计营业额")
    private String cumulativeTurnoverToLastPeriod;

    @ApiModelProperty("截止本月本年累计营业额")
    @Excel(name = "截止本月本年累计营业额")
    private String cumulativeTurnoverToCurrentMonth;

    @ApiModelProperty("截止本月本年累计销项税额")
    @Excel(name = "截止本月本年累计销项税额")
    private String cumulativeOutputTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计加速加计扣除进项税额")
    @Excel(name = "截止本月本年累计加速加计扣除进项税额")
    private String cumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计预计缴纳增值税额")
    @Excel(name = "截止本月本年累计预计缴纳增值税额")
    private String cumulativeExpectedVatToCurrentMonth;

    @ApiModelProperty("截止本月预计增值税税负率")
    @Excel(name = "截止本月预计增值税税负率")
    private String estimatedVatBurdenRateToCurrentMonth;

    @ApiModelProperty("截止本月本年累计实际抵扣进项税额")
    @Excel(name = "截止本月本年累计实际抵扣进项税额")
    private String cumulativeActualDeductedInputTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计进项发票金额（不含税）")
    @Excel(name = "截止本月本年累计进项发票金额（不含税）")
    private String cumulativeInputInvoiceAmountExcludingTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计进项发票金额（含税）")
    @Excel(name = "截止本月本年累计进项发票金额（含税）")
    private String cumulativeInputInvoiceAmountIncludingTaxToCurrentMonth;

    public String getLastPeriodCarriedForwardTax() {
        return lastPeriodCarriedForwardTax;
    }

    public void setLastPeriodCarriedForwardTax(String lastPeriodCarriedForwardTax) {
        this.lastPeriodCarriedForwardTax = lastPeriodCarriedForwardTax;
    }

    public String getCurrentCertifiedInputAmount() {
        return currentCertifiedInputAmount;
    }

    public void setCurrentCertifiedInputAmount(String currentCertifiedInputAmount) {
        this.currentCertifiedInputAmount = currentCertifiedInputAmount;
    }

    public String getCurrentPendingInputAmount() {
        return currentPendingInputAmount;
    }

    public void setCurrentPendingInputAmount(String currentPendingInputAmount) {
        this.currentPendingInputAmount = currentPendingInputAmount;
    }

    public String getCurrentMonthOrQuarterSalesAmount() {
        return currentMonthOrQuarterSalesAmount;
    }

    public void setCurrentMonthOrQuarterSalesAmount(String currentMonthOrQuarterSalesAmount) {
        this.currentMonthOrQuarterSalesAmount = currentMonthOrQuarterSalesAmount;
    }

    public String getUnInvoicedIncome() {
        return unInvoicedIncome;
    }

    public void setUnInvoicedIncome(String unInvoicedIncome) {
        this.unInvoicedIncome = unInvoicedIncome;
    }

    public String getCurrentOutputTax() {
        return currentOutputTax;
    }

    public void setCurrentOutputTax(String currentOutputTax) {
        this.currentOutputTax = currentOutputTax;
    }

    public String getCurrentSimpleTaxCollection() {
        return currentSimpleTaxCollection;
    }

    public void setCurrentSimpleTaxCollection(String currentSimpleTaxCollection) {
        this.currentSimpleTaxCollection = currentSimpleTaxCollection;
    }

    public String getCurrentCertifiedInputTax() {
        return currentCertifiedInputTax;
    }

    public void setCurrentCertifiedInputTax(String currentCertifiedInputTax) {
        this.currentCertifiedInputTax = currentCertifiedInputTax;
    }

    public String getCurrentPendingInputTax() {
        return currentPendingInputTax;
    }

    public void setCurrentPendingInputTax(String currentPendingInputTax) {
        this.currentPendingInputTax = currentPendingInputTax;
    }

    public String getCurrentSelectedInputTax() {
        return currentSelectedInputTax;
    }

    public void setCurrentSelectedInputTax(String currentSelectedInputTax) {
        this.currentSelectedInputTax = currentSelectedInputTax;
    }

    public String getPendingInputTaxForCalculation() {
        return pendingInputTaxForCalculation;
    }

    public void setPendingInputTaxForCalculation(String pendingInputTaxForCalculation) {
        this.pendingInputTaxForCalculation = pendingInputTaxForCalculation;
    }

    public String getCurrentSelectedAndCertifiedInputTax() {
        return currentSelectedAndCertifiedInputTax;
    }

    public void setCurrentSelectedAndCertifiedInputTax(String currentSelectedAndCertifiedInputTax) {
        this.currentSelectedAndCertifiedInputTax = currentSelectedAndCertifiedInputTax;
    }

    public String getCurrentRedLetterInputTaxShouldRevert() {
        return currentRedLetterInputTaxShouldRevert;
    }

    public void setCurrentRedLetterInputTaxShouldRevert(String currentRedLetterInputTaxShouldRevert) {
        this.currentRedLetterInputTaxShouldRevert = currentRedLetterInputTaxShouldRevert;
    }

    public String getCurrentExpectedPayableVat() {
        return currentExpectedPayableVat;
    }

    public void setCurrentExpectedPayableVat(String currentExpectedPayableVat) {
        this.currentExpectedPayableVat = currentExpectedPayableVat;
    }

    public String getCurrentPaidVat() {
        return currentPaidVat;
    }

    public void setCurrentPaidVat(String currentPaidVat) {
        this.currentPaidVat = currentPaidVat;
    }

    public String getCurrentActualReportedInputTax() {
        return currentActualReportedInputTax;
    }

    public void setCurrentActualReportedInputTax(String currentActualReportedInputTax) {
        this.currentActualReportedInputTax = currentActualReportedInputTax;
    }

    public String getCurrentExpectedRemainingInputTax() {
        return currentExpectedRemainingInputTax;
    }

    public void setCurrentExpectedRemainingInputTax(String currentExpectedRemainingInputTax) {
        this.currentExpectedRemainingInputTax = currentExpectedRemainingInputTax;
    }

    public String getCurrentInvoiceCount() {
        return currentInvoiceCount;
    }

    public void setCurrentInvoiceCount(String currentInvoiceCount) {
        this.currentInvoiceCount = currentInvoiceCount;
    }

    public String getLastYearVatBurdenRate() {
        return lastYearVatBurdenRate;
    }

    public void setLastYearVatBurdenRate(String lastYearVatBurdenRate) {
        this.lastYearVatBurdenRate = lastYearVatBurdenRate;
    }

    public String getCumulativeVatBurdenRateToLastPeriod() {
        return cumulativeVatBurdenRateToLastPeriod;
    }

    public void setCumulativeVatBurdenRateToLastPeriod(String cumulativeVatBurdenRateToLastPeriod) {
        this.cumulativeVatBurdenRateToLastPeriod = cumulativeVatBurdenRateToLastPeriod;
    }

    public String getCumulativeVatPaidToLastPeriod() {
        return cumulativeVatPaidToLastPeriod;
    }

    public void setCumulativeVatPaidToLastPeriod(String cumulativeVatPaidToLastPeriod) {
        this.cumulativeVatPaidToLastPeriod = cumulativeVatPaidToLastPeriod;
    }

    public String getCumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth() {
        return cumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth;
    }

    public void setCumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth(String cumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth) {
        this.cumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth = cumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth;
    }

    public String getCumulativeTurnoverToLastPeriod() {
        return cumulativeTurnoverToLastPeriod;
    }

    public void setCumulativeTurnoverToLastPeriod(String cumulativeTurnoverToLastPeriod) {
        this.cumulativeTurnoverToLastPeriod = cumulativeTurnoverToLastPeriod;
    }

    public String getCumulativeTurnoverToCurrentMonth() {
        return cumulativeTurnoverToCurrentMonth;
    }

    public void setCumulativeTurnoverToCurrentMonth(String cumulativeTurnoverToCurrentMonth) {
        this.cumulativeTurnoverToCurrentMonth = cumulativeTurnoverToCurrentMonth;
    }

    public String getCumulativeOutputTaxToCurrentMonth() {
        return cumulativeOutputTaxToCurrentMonth;
    }

    public void setCumulativeOutputTaxToCurrentMonth(String cumulativeOutputTaxToCurrentMonth) {
        this.cumulativeOutputTaxToCurrentMonth = cumulativeOutputTaxToCurrentMonth;
    }

    public String getCumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth() {
        return cumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth;
    }

    public void setCumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth(String cumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth) {
        this.cumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth = cumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth;
    }

    public String getCumulativeExpectedVatToCurrentMonth() {
        return cumulativeExpectedVatToCurrentMonth;
    }

    public void setCumulativeExpectedVatToCurrentMonth(String cumulativeExpectedVatToCurrentMonth) {
        this.cumulativeExpectedVatToCurrentMonth = cumulativeExpectedVatToCurrentMonth;
    }

    public String getEstimatedVatBurdenRateToCurrentMonth() {
        return estimatedVatBurdenRateToCurrentMonth;
    }

    public void setEstimatedVatBurdenRateToCurrentMonth(String estimatedVatBurdenRateToCurrentMonth) {
        this.estimatedVatBurdenRateToCurrentMonth = estimatedVatBurdenRateToCurrentMonth;
    }

    public String getCumulativeActualDeductedInputTaxToCurrentMonth() {
        return cumulativeActualDeductedInputTaxToCurrentMonth;
    }

    public void setCumulativeActualDeductedInputTaxToCurrentMonth(String cumulativeActualDeductedInputTaxToCurrentMonth) {
        this.cumulativeActualDeductedInputTaxToCurrentMonth = cumulativeActualDeductedInputTaxToCurrentMonth;
    }

    public String getCumulativeInputInvoiceAmountExcludingTaxToCurrentMonth() {
        return cumulativeInputInvoiceAmountExcludingTaxToCurrentMonth;
    }

    public void setCumulativeInputInvoiceAmountExcludingTaxToCurrentMonth(String cumulativeInputInvoiceAmountExcludingTaxToCurrentMonth) {
        this.cumulativeInputInvoiceAmountExcludingTaxToCurrentMonth = cumulativeInputInvoiceAmountExcludingTaxToCurrentMonth;
    }

    public String getCumulativeInputInvoiceAmountIncludingTaxToCurrentMonth() {
        return cumulativeInputInvoiceAmountIncludingTaxToCurrentMonth;
    }

    public void setCumulativeInputInvoiceAmountIncludingTaxToCurrentMonth(String cumulativeInputInvoiceAmountIncludingTaxToCurrentMonth) {
        this.cumulativeInputInvoiceAmountIncludingTaxToCurrentMonth = cumulativeInputInvoiceAmountIncludingTaxToCurrentMonth;
    }

    public String getCumulativeInvoicedAmountLast12Months() {
        return cumulativeInvoicedAmountLast12Months;
    }

    public void setCumulativeInvoicedAmountLast12Months(String cumulativeInvoicedAmountLast12Months) {
        this.cumulativeInvoicedAmountLast12Months = cumulativeInvoicedAmountLast12Months;
    }

    @ApiModelProperty("近12个月累计开票金额")
    @Excel(name = "近12个月累计开票金额")
    private String cumulativeInvoicedAmountLast12Months;

    @Excel(name = "认证备注")
    private String authRemark;

    @Excel(name = "提醒文案")
    private String preAuthRemind;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getAuthResult() {
        return authResult;
    }

    public void setAuthResult(String authResult) {
        this.authResult = authResult;
    }

    public String getAuthRemark() {
        return authRemark;
    }

    public void setAuthRemark(String authRemark) {
        this.authRemark = authRemark;
    }

    public String getCheckError() {
        return checkError;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public String getPreAuthRemind() {
        return preAuthRemind;
    }

    public void setPreAuthRemind(String preAuthRemind) {
        this.preAuthRemind = preAuthRemind;
    }
}
