package com.bxm.file;

import com.bxm.common.security.annotation.EnableCustomConfig;
import com.bxm.common.security.annotation.EnableJsFeignClients;
import com.bxm.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 第三方模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableJsFeignClients
@SpringBootApplication
@EnableAsync
public class BxmThirdpartApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(BxmThirdpartApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  第三方模块启动成功   ");
    }
}
