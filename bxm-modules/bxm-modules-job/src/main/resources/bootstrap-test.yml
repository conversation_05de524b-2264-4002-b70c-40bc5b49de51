spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 120.27.156.42:18848
        namespace: test
      config:
        # 配置中心地址
        server-addr: 120.27.156.42:18848
        # 配置文件格式
        file-extension: yaml
        refresh-enabled: true
        namespace: test
        group: bxm-job
        name: bxm-job
        extension-configs: # 此处加载分离的扩展配置，例如抽取数据库、mybats配置等
          - data-id: redis.yaml
            group: bxm-common
            refresh: true

          - data-id: datasource.yaml
            group: bxm-common
            refresh: true