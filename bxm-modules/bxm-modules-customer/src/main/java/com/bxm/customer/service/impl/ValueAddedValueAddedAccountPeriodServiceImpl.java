package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.ValueAddedPeriodMonth;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.ICustomerServicePeriodMonthService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.customer.service.IValueAddedPeriodMonthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 账期服务实现类
 *
 * 处理增值交付单状态变更时的账期相关操作，包括校验、生成和删除账期记录
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ValueAddedValueAddedAccountPeriodServiceImpl implements ValueAddedAccountPeriodService {

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private IValueAddedPeriodMonthService valueAddedPeriodMonthService;

    @Override
    public void validateAccountPeriodForSubmit(ValueAddedDeliveryOrder order) {
        try {
            log.info("Starting account period validation for order: {}, valueAddedItemTypeId: {}", order.getDeliveryOrderNo(), order.getValueAddedItemTypeId());

            // 1. 根据valueAddedItemTypeId获取itemName
            String itemName = getItemNameByTypeId(order.getValueAddedItemTypeId());
            if (itemName == null) {
                log.warn("Item name not found for valueAddedItemTypeId: {}", order.getValueAddedItemTypeId());
                return; // 如果找不到对应的事项名称，跳过校验
            }

            log.info("Found item name: {} for order: {}", itemName, order.getDeliveryOrderNo());

            // 2. 根据itemName执行不同的校验逻辑
            if ("补账".equals(itemName)) {
                validateSupplementAccount(order);
            } else if ("改账".equals(itemName)) {
                validateModifyAccount(order);
            } else {
                log.info("Item name '{}' does not require period validation, skipping", itemName);
            }

            log.info("Account period validation completed successfully for order: {}", order.getDeliveryOrderNo());

        } catch (IllegalArgumentException e) {
            log.error("Account period validation failed for order: {}, error: {}", order.getDeliveryOrderNo(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during account period validation for order: {}", order.getDeliveryOrderNo(), e);
            throw new RuntimeException("账期校验过程中发生系统异常", e);
        }
    }

    /**
     * 根据增值事项类型ID获取事项名称
     */
    private String getItemNameByTypeId(Integer valueAddedItemTypeId) {
        if (valueAddedItemTypeId == null) {
            return null;
        }

        try {
            ValueAddedItemType itemType = valueAddedItemTypeService.getById(valueAddedItemTypeId.longValue());
            return itemType != null ? itemType.getItemName() : null;
        } catch (Exception e) {
            log.error("Failed to get item name for valueAddedItemTypeId: {}", valueAddedItemTypeId, e);
            return null;
        }
    }

    /**
     * 校验补账类型：如果在账期范围内存在period记录，则报错
     */
    private void validateSupplementAccount(ValueAddedDeliveryOrder order) {
        log.info("Validating supplement account for order: {}", order.getDeliveryOrderNo());

        // 检查必要字段
        validateRequiredFields(order);

        // 查询CustomerServicePeriodMonth表
        boolean existsInCustomerService = customerServicePeriodMonthService.checkPeriodExists(
                order.getBusinessTopDeptId(),
                order.getCreditCode(),
                order.getAccountingPeriodStart(),
                order.getAccountingPeriodEnd()
        );

        // 查询ValueAddedPeriodMonth表
        boolean existsInValueAdded = valueAddedPeriodMonthService.checkPeriodExists(
                order.getBusinessTopDeptId(),
                order.getCreditCode(),
                order.getAccountingPeriodStart(),
                order.getAccountingPeriodEnd()
        );

        // 如果任一表中存在记录，则报错
        if (existsInCustomerService || existsInValueAdded) {
            String message = "当前存在部分账期不允许提交";
            log.warn("Supplement account validation failed for order: {}, reason: {}", order.getDeliveryOrderNo(), message);
            throw new IllegalArgumentException(message);
        }

        log.info("Supplement account validation passed for order: {}", order.getDeliveryOrderNo());
    }

    /**
     * 校验改账类型：如果在账期范围内不存在任何period记录，则报错
     */
    private void validateModifyAccount(ValueAddedDeliveryOrder order) {
        log.info("Validating modify account for order: {}", order.getDeliveryOrderNo());

        // 检查必要字段
        validateRequiredFields(order);

        // 查询CustomerServicePeriodMonth表
        boolean existsInCustomerService = customerServicePeriodMonthService.checkPeriodExists(
                order.getBusinessTopDeptId(),
                order.getCreditCode(),
                order.getAccountingPeriodStart(),
                order.getAccountingPeriodEnd()
        );

        // 查询ValueAddedPeriodMonth表
        boolean existsInValueAdded = valueAddedPeriodMonthService.checkPeriodExists(
                order.getBusinessTopDeptId(),
                order.getCreditCode(),
                order.getAccountingPeriodStart(),
                order.getAccountingPeriodEnd()
        );

        // 如果两个表中都不存在记录，则报错
        if (!existsInCustomerService && !existsInValueAdded) {
            String message = "当前账期不存在不允许提交";
            log.warn("Modify account validation failed for order: {}, reason: {}", order.getDeliveryOrderNo(), message);
            throw new IllegalArgumentException(message);
        }

        log.info("Modify account validation passed for order: {}", order.getDeliveryOrderNo());
    }

    /**
     * 校验必要字段
     */
    private void validateRequiredFields(ValueAddedDeliveryOrder order) {
        if (order.getBusinessTopDeptId() == null) {
            throw new IllegalArgumentException("集团ID不能为空");
        }
        if (order.getCreditCode() == null || order.getCreditCode().trim().isEmpty()) {
            throw new IllegalArgumentException("统一社会信用代码不能为空");
        }
        if (order.getAccountingPeriodStart() == null) {
            throw new IllegalArgumentException("账期开始时间不能为空");
        }
        if (order.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("账期结束时间不能为空");
        }
    }

    @Override
    public void generateValueAddedPeriodRecords(ValueAddedDeliveryOrder order) {
        try {
            log.info("Starting to generate ValueAddedPeriodMonth records for order: {}", order.getDeliveryOrderNo());

            // 校验必要字段
            validateRequiredFieldsForPeriodOperation(order);

            // 生成账期范围内的所有月份记录
            List<ValueAddedPeriodMonth> periodRecords = createPeriodRecords(order);

            if (!periodRecords.isEmpty()) {
                // 批量保存记录
                valueAddedPeriodMonthService.saveBatch(periodRecords);
                log.info("Successfully generated {} ValueAddedPeriodMonth records for order: {}", periodRecords.size(), order.getDeliveryOrderNo());
            } else {
                log.warn("No period records to generate for order: {}", order.getDeliveryOrderNo());
            }

        } catch (Exception e) {
            log.error("Failed to generate ValueAddedPeriodMonth records for order: {}", order.getDeliveryOrderNo(), e);
            throw new RuntimeException("生成增值账期记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteValueAddedPeriodRecords(ValueAddedDeliveryOrder order) {
        try {
            log.info("Starting to delete ValueAddedPeriodMonth records for order: {}", order.getDeliveryOrderNo());

            // 校验必要字段
            validateRequiredFieldsForPeriodOperation(order);

            // 构建删除条件
            LambdaQueryWrapper<ValueAddedPeriodMonth> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedPeriodMonth::getBusinessTopDeptId, order.getBusinessTopDeptId())
                       .eq(ValueAddedPeriodMonth::getCreditCode, order.getCreditCode())
                       .ge(ValueAddedPeriodMonth::getPeriod, order.getAccountingPeriodStart())
                       .le(ValueAddedPeriodMonth::getPeriod, order.getAccountingPeriodEnd());

            // 执行删除
            boolean result = valueAddedPeriodMonthService.remove(queryWrapper);

            if (result) {
                log.info("Successfully deleted ValueAddedPeriodMonth records for order: {}", order.getDeliveryOrderNo());
            } else {
                log.warn("No ValueAddedPeriodMonth records found to delete for order: {}", order.getDeliveryOrderNo());
            }

        } catch (Exception e) {
            log.error("Failed to delete ValueAddedPeriodMonth records for order: {}", order.getDeliveryOrderNo(), e);
            throw new RuntimeException("删除增值账期记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验账期操作所需的必要字段
     */
    private void validateRequiredFieldsForPeriodOperation(ValueAddedDeliveryOrder order) {
        if (order.getBusinessTopDeptId() == null) {
            throw new IllegalArgumentException("业务顶级部门ID不能为空");
        }
        if (order.getCreditCode() == null || order.getCreditCode().trim().isEmpty()) {
            throw new IllegalArgumentException("统一社会信用代码不能为空");
        }
        if (order.getAccountingPeriodStart() == null) {
            throw new IllegalArgumentException("账期开始时间不能为空");
        }
        if (order.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("账期结束时间不能为空");
        }
        if (order.getAccountingPeriodStart() > order.getAccountingPeriodEnd()) {
            throw new IllegalArgumentException("账期开始时间不能大于结束时间");
        }
    }

    /**
     * 创建账期范围内的所有月份记录
     */
    private List<ValueAddedPeriodMonth> createPeriodRecords(ValueAddedDeliveryOrder order) {
        List<ValueAddedPeriodMonth> records = new ArrayList<>();

        // 解析开始和结束账期
        LocalDate startDate = parseAccountingPeriod(order.getAccountingPeriodStart());
        LocalDate endDate = parseAccountingPeriod(order.getAccountingPeriodEnd());

        // 生成范围内的所有月份记录
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            Integer period = Integer.parseInt(currentDate.format(DateTimeFormatter.ofPattern("yyyyMM")));

            ValueAddedPeriodMonth record = new ValueAddedPeriodMonth();
            record.setPeriod(period);
            record.setBusinessTopDeptId(order.getBusinessTopDeptId());
            record.setCreditCode(order.getCreditCode());
            record.setCustomerName(order.getCustomerName());

            // 设置默认值
            record.setServiceStatus(1); // 1-服务中

            // 如果有业务部门ID，设置相关字段
            if (order.getBusinessDeptId() != null) {
                record.setBusinessDeptId(order.getBusinessDeptId());
            }

            records.add(record);

            // 移动到下一个月
            currentDate = currentDate.plusMonths(1);
        }

        return records;
    }

    /**
     * 解析账期格式（YYYYMM）为LocalDate
     */
    private LocalDate parseAccountingPeriod(Integer period) {
        if (period == null) {
            throw new IllegalArgumentException("账期不能为空");
        }

        String periodStr = period.toString();
        if (periodStr.length() != 6) {
            throw new IllegalArgumentException("账期格式错误，应为YYYYMM格式");
        }

        try {
            // 将YYYYMM格式转换为当月第一天
            return LocalDate.parse(periodStr + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            throw new IllegalArgumentException("账期格式错误，无法解析: " + period, e);
        }
    }
}
