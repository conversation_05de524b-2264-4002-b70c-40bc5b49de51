package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.strategy.StatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 待提交状态变更策略
 *
 * 处理从"已保存待提交"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. SAVED_PENDING_SUBMIT -> SUBMITTED_PENDING_DELIVERY (正常提交)
 * 2. SAVED_PENDING_SUBMIT -> DRAFT (退回草稿)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class PendingSubmitChangeStrategy implements StatusChangeStrategy {

    @Autowired
    private ValueAddedAccountPeriodService valueAddedAccountPeriodService;

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
               targetStatus == ValueAddedDeliveryOrderStatus.DRAFT;
    }

    @Override
    public void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        log.info("Processing status change from SAVED_PENDING_SUBMIT to {} for order: {}", request.getTargetStatus(), request.getDeliveryOrderNo());

        ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

        if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            // 验证提交到已提交待交付状态
            validateSubmitToDelivery(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DRAFT) {
            // 验证退回到草稿状态
            validateReturnToDraft(order, request);
        } else {
            throw new IllegalArgumentException("不支持从待提交状态转换到: " + request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT;
    }

    /**
     * 验证提交到已提交待交付状态
     */
    private void validateSubmitToDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证必要字段是否完整
        if (order.getCustomerName() == null || order.getCustomerName().trim().isEmpty()) {
            throw new IllegalArgumentException("客户企业名称不能为空");
        }

        if (order.getCreditCode() == null || order.getCreditCode().trim().isEmpty()) {
            throw new IllegalArgumentException("统一社会信用代码不能为空");
        }

        if (order.getValueAddedItemTypeId() == null) {
            throw new IllegalArgumentException("增值事项类型不能为空");
        }

        if (order.getDdl() == null) {
            throw new IllegalArgumentException("交付截止日期不能为空");
        }

        // 验证操作人权限（提交人角色验证）
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 执行账期校验（补账/改账类型的特殊校验）
        try {
            valueAddedAccountPeriodService.validateAccountPeriodForSubmit(order);
            log.info("Account period validation passed for order: {}", request.getDeliveryOrderNo());
        } catch (IllegalArgumentException e) {
            log.error("Account period validation failed for order: {}, error: {}", request.getDeliveryOrderNo(), e.getMessage());
            throw e; // 重新抛出校验异常
        } catch (Exception e) {
            log.error("Unexpected error during account period validation for order: {}", request.getDeliveryOrderNo(), e);
            throw new RuntimeException("账期校验过程中发生系统异常: " + e.getMessage(), e);
        }

        // 生成增值账期记录
        try {
            valueAddedAccountPeriodService.generateValueAddedPeriodRecords(order);
            log.info("ValueAddedPeriodMonth records generated successfully for order: {}", request.getDeliveryOrderNo());
        } catch (Exception e) {
            log.error("Failed to generate ValueAddedPeriodMonth records for order: {}", request.getDeliveryOrderNo(), e);
            throw new RuntimeException("生成增值账期记录失败: " + e.getMessage(), e);
        }

        log.info("Submit validation passed for order: {}", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回到草稿状态
     */
    private void validateReturnToDraft(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 验证操作人权限
        if (request.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证是否有退回原因
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new IllegalArgumentException("退回草稿状态必须提供原因");
        }

        log.info("Return to draft validation passed for order: {}", request.getDeliveryOrderNo());
    }
}
