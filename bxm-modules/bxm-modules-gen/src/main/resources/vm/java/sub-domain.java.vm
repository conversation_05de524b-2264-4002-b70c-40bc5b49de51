package ${packageName}.domain;

#foreach ($import in $subImportList)
import ${import};
#end
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * ${subTable.functionName}对象 ${subTableName}
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@ApiModel("${subTable.functionName}对象")
public class ${subClassName} extends BaseEntity
{
    private static final long serialVersionUID = 1L;

#foreach ($column in $subTable.columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'LocalDateTime')
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
#elseif($column.javaType == 'LocalDate')
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#elseif($column.javaType == 'LocalTime')
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "HH:mm:ss")
#else
    @Excel(name = "${comment}")
#end
#end
    #if($column.isPk == 1)
        #if($column.isIncrement == 1)
        @TableId(value = "${column.columnName}", type = IdType.AUTO)
        @Excel(name = "$column.columnComment")
        #else
        @TableId(value = "${column.columnName}", type = IdType.ASSIGN_ID)
        @Excel(name = "$column.columnComment")
        #end
    #else
    @TableField("${column.columnName}")
    #end
    @ApiModelProperty(value = "${column.columnComment}")
    private $column.javaType $column.javaField;

#end
#end
}
