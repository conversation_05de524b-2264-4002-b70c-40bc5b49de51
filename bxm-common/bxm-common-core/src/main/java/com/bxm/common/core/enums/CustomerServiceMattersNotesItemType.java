package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomerServiceMattersNotesItemType {

    // 事项类型，1-空（为了和交付单类型一致，医社保用2），2-医社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报，9-银行流水，10-入账，11-财税风控，12-税种核定，13-险种核定，14-发票获取，15-残保金
    EMPTY(1, "空"),
    MEDICAL_INSURANCE(2, "医社保"),
    TAX(3, "个税（工资薪金）"),
    TAX_FOR_NATIONAL_ACCOUNTING(4, "国税"),
    PRE_AUTHENTICATION(5, "预认证"),
    TAX_FOR_MANAGEMENT(6, "个税（经营所得）"),
    HUISUAN(7, "汇算"),
    ANNUAL_REPORT(8, "年报"),
    BANK_PAYMENT(9, "银行流水"),
    IN_ACCOUNT(10, "入账"),
    TAX_RISK_CONTROL(11, "财税风控"),
    TAX_TYPE_ADJUSTMENT(12, "税种核定"),
    INSURANCE_TYPE_ADJUSTMENT(13, "险种核定"),
    INVOICE_ACQUISITION(14, "发票获取"),
    RESIDUAL_BENEFITS(15, "残保金"),
    TIMES_REPORT(16, "次报"),
    ;
    private final Integer code;
    private final String name;

    public static String getNameByCode(Integer code) {
        for (CustomerServiceMattersNotesItemType itemType : CustomerServiceMattersNotesItemType.values()) {
            if (itemType.getCode().equals(code)) {
                return itemType.getName();
            }
        }
        return "";
    }
}
