package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SysAccountType {

    MEDICAL_INSURANCE(1, "医保"),
    SOCIAL_INSURANCE(2, "社保"),
    PROFIT_SHARING(3, "公积金"),
    TAX(4, "个税（工资薪金）"),
    NATIONAL_TAX(5, "国税"),
    OPERATION_TAX(6, "个税（经营所得）"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static SysAccountType getByCode(Integer code) {
        for (SysAccountType sysAccountType : SysAccountType.values()) {
            if (sysAccountType.getCode().equals(code)) {
                return sysAccountType;
            }
        }
        return UN_KNOW;
    }

    public static List<String> getSystemSysAccountType() {
        return Arrays.stream(values()).filter(row -> !Objects.equals(row.getCode(), UN_KNOW.getCode()))
                .map(SysAccountType::getName).collect(Collectors.toList());
    }
}
