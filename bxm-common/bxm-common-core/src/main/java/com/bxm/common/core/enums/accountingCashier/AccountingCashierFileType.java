package com.bxm.common.core.enums.accountingCashier;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccountingCashierFileType {

    // 文件类型，1-介质材料附件，2-交付附件，3-rpa附件
    MEDIA_MATERIAL_ATTACHMENT(1, "介质材料附件/对账单文件"),
    DELIVER_ATTACHMENT(2, "交付附件"),
    RPA_ATTACHMENT(3, "rpa附件"),
    RECEIPT_ATTACHMENT(4, "回单文件"),
    ;

    private final Integer code;

    private final String name;
}
