package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaxMethod {

    // 个税申报方式, 1-易捷账, 2-扣缴端
    YIJIE_ZHANG(1, "易捷账"),
    KUO_JIAO_DUAN(2, "扣缴端"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static TaxMethod getByCode(Integer code) {
        for (TaxMethod taxMethod : TaxMethod.values()) {
            if (taxMethod.getCode().equals(code)) {
                return taxMethod;
            }
        }
        return UN_KNOW;
    }
}
