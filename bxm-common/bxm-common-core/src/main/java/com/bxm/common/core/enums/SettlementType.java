package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SettlementType {

    // 结算类型，1-入账结算，2-新户预收
    IN_ACCOUNT(1, "入账结算", "账期"),
    NEW_USER_PREPAYMENT(2, "新户预收", "户"),

    TASK_PERIOD(3, "账期任务", "账期"),
    TASK_CUSTOMER(4, "服务任务", "户"),

    IN_ACCOUNT_PREPAYMENT(5, "账期预收", "账期"),

    UN_KNOW(99, "未知", "未知"),
    ;

    private final Integer code;

    private final String name;

    private final String unit;

    public static SettlementType getByCode(Integer code) {
        for (SettlementType item : SettlementType.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
