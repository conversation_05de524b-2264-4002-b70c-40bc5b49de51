package com.bxm.common.core.enums.accountingCashier;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum AccountingCashierType {

    // 账务类型，1-入账，2-流水，3-改账
    INCOME(1, "入账"),
    <PERSON>OW(2, "流水"),
    CHANGE(3, "改账"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static AccountingCashierType getByCode(Integer code) {
        for (AccountingCashierType value : AccountingCashierType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UN_KNOW;
    }

    public static Integer convertToItemType(Integer type) {
        if (Objects.equals(type, INCOME.getCode())) {
            return 10;
        }
        if (Objects.equals(type, FLOW.getCode())) {
            return 9;
        }
        return null;
    }
}
