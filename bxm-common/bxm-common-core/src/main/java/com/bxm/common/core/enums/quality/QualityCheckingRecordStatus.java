package com.bxm.common.core.enums.quality;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QualityCheckingRecordStatus {

    // 质检记录状态，1-进行中，2-已完成，3-超时关闭，4-已关闭，5-失败关闭，99-未知
    UNEXECUTED(1, "进行中"),
    EXECUTING(2, "已完成"),
    EXECUTED(3, "超时关闭"),
    UNCLOSED(4, "已关闭"),
    FAILED(5, "失败关闭"),
    UNKNOWN(99, "未知");

    private final Integer code;

    private final String name;

    public static QualityCheckingRecordStatus getByCode(Integer code) {
        for (QualityCheckingRecordStatus value : QualityCheckingRecordStatus.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
