package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialDeliverPushResult {

    // 推送结果，1-成功，2-失败
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static MaterialDeliverPushResult getByCode(Integer code) {
        for (MaterialDeliverPushResult result : MaterialDeliverPushResult.values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }
        return UN_KNOW;
    }
}
