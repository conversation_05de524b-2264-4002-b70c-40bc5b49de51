package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum IncomeType {

    // 收入类型，1-收入，2-支出
    INCOME(1, "收入"),
    EXPENDITURE(2, "支出"),

    UN_KNOW(99, "未知"),
    ;

    private final Integer code;
    private final String desc;

    public static IncomeType getByCode(Integer code) {
        for (IncomeType incomeType : IncomeType.values()) {
            if (incomeType.getCode().equals(code)) {
                return incomeType;
            }
        }
        return UN_KNOW;
    }

    public static IncomeType allotTypeToIncomeType(Integer allotType) {
        if (Objects.equals(allotType, AllotType.IN.getCode())) {
            return INCOME;
        } else if (Objects.equals(allotType, AllotType.OUT.getCode())) {
            return EXPENDITURE;
        }
        return UN_KNOW;
    }
}
