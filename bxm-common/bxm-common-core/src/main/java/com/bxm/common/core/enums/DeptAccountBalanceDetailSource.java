package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DeptAccountBalanceDetailSource {

    // 来源类型，1-结算单（新户预收），2-账单（预存抵扣），3-调入，4-拨出
    SETTLEMENT_ORDER(1, "结算单"),
    BILL(2, "账单"),
    IN(3, "调入"),
    OUT(4, "拨出"),

    UN_KNOW(99, "未知"),
    ;

    private final Integer code;
    private final String desc;

    public static DeptAccountBalanceDetailSource getByCode(Integer code) {
        for (DeptAccountBalanceDetailSource item : DeptAccountBalanceDetailSource.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static DeptAccountBalanceDetailSource allotTypeToSource(Integer allotType) {
        if (Objects.equals(allotType, AllotType.IN.getCode())) {
            return IN;
        } else if (Objects.equals(allotType, AllotType.OUT.getCode())) {
            return OUT;
        }
        return UN_KNOW;
    }
}
