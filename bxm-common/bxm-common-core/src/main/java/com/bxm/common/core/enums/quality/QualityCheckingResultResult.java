package com.bxm.common.core.enums.quality;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QualityCheckingResultResult {

    // 质检结果结果，1-正常，2-异常，99-未知
    NORMAL(1, "正常"),
    EXCEPTION(2, "异常"),
    UNKNOWN(99, "未知");

    private final Integer code;

    private final String name;

    public static QualityCheckingResultResult getByCode(Integer code) {
        for (QualityCheckingResultResult value : QualityCheckingResultResult.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
