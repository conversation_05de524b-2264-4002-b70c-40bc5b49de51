package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum MaterialDeliverAnalysisStatus {

    // 解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止
    QUEUE(0, "队列中"),
    PARSING(1, "解析中"),
    PARSE_SUCCESS(2, "解析完成"),
    PARSE_FAIL(3, "解析失败"),
    PARSE_STOP(4, "解析中止"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static MaterialDeliverAnalysisStatus getByCode(Integer code) {
        for (MaterialDeliverAnalysisStatus status : MaterialDeliverAnalysisStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_KNOW;
    }

    public static List<Integer> canDeleteAnalysisStatus() {
        return Arrays.asList(PARSE_FAIL.getCode(), PARSE_STOP.getCode());
    }
}
