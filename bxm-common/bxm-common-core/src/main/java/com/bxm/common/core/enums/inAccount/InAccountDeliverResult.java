package com.bxm.common.core.enums.inAccount;

import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/17 10:14
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum InAccountDeliverResult {
    UN_KNOW(-1, "未知"),

    NORMAL(1, "正常"),
    NOT_NEED_DELIVER(2, "无需交付"),
    EXCEPTION(3, "异常"),
    NO_ACCOUNTING(4, "无账务"),
    ;

    private final Integer code;

    private final String name;

    private static List<Integer> UN_NORMAL = Arrays.asList(NOT_NEED_DELIVER.getCode(), EXCEPTION.getCode());

    public static InAccountDeliverResult getByCode(Integer source) {
        for (InAccountDeliverResult item : InAccountDeliverResult.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static String getMultiNamesByCodes(String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }
        String[] split = codes.split(",");
        return Arrays.stream(split).map(row -> getByCode(Integer.parseInt(row)).getName()).collect(Collectors.joining(","));
    }

    public static InAccountDeliverResult getByName(String source) {
        for (InAccountDeliverResult item : InAccountDeliverResult.values()) {
            if (Objects.equals(source, item.getName())) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static Boolean unNormal(Integer code) {
        return UN_NORMAL.contains(code);
    }

    public static InAccountDeliverResult convertFromInAccountResult(Integer inAccountResult) {
        switch (inAccountResult) {
            case 1:
                return NORMAL;
            case 2:
                return EXCEPTION;
            case 3:
                return NOT_NEED_DELIVER;
            case 4:
                return NO_ACCOUNTING;
            default:
                return UN_KNOW;
        }
    }
}
