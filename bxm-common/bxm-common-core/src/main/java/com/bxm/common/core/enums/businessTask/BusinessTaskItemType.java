package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/27 10:09
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum BusinessTaskItemType {
    UN_KNOW(-1, "未知"),

    BANK_PAYMENT(1, "银行流水"),
    ;

    private final Integer code;

    private final String name;

    public static BusinessTaskItemType getByCode(Integer source) {
        for (BusinessTaskItemType item : BusinessTaskItemType.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

}
