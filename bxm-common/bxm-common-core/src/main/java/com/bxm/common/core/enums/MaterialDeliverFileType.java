package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialDeliverFileType {

    // 文件类型，1-文件清单，2-附件文件，3-异常文件
    FILE_LIST(1, "文件清单"),
    ATTACHMENT_FILE(2, "附件文件"),
    EXCEPTION_FILE(3, "异常文件"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static MaterialDeliverFileType getByCode(Integer code) {
        for (MaterialDeliverFileType type : MaterialDeliverFileType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return UN_KNOW;
    }
}
