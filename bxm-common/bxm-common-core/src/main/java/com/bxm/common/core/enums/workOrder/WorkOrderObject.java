package com.bxm.common.core.enums.workOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkOrderObject {

    // 工单处理方，1-发起方，2-承接方，99-未知
    INITIATE_PARTY(1, "发起人"),
    UNDERTAKE_PARTY(2, "承接人"),
    UNKNOWN(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static WorkOrderObject getByCode(Integer code) {
        for (WorkOrderObject workOrderObject : WorkOrderObject.values()) {
            if (workOrderObject.getCode().equals(code)) {
                return workOrderObject;
            }
        }
        return UNKNOWN;
    }
}
