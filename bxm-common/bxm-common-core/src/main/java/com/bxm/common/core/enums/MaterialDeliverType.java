package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialDeliverType {

    // 交接单类型，1-银行流水，2-普通入账，3-凭票入账
    BANK_FLOW(1, "银行流水"),
    NORMAL_IN_ACCOUNT(2, "普通入账"),
    TICKET_IN_ACCOUNT(3, "凭票入账"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static MaterialDeliverType getByCode(Integer code) {
        for (MaterialDeliverType item : MaterialDeliverType.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
