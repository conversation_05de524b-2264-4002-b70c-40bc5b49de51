package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CoverFileType {

    // 补充材料方式，1-追加，2-覆盖，3-无需补充
    ADD(1, "追加"),
    COVER(2, "覆盖"),
    NO_SUPPLEMENT(3, "无需补充");

    private final Integer code;
    private final String desc;

    public static CoverFileType getByCode(Integer code) {
        for (CoverFileType coverFileType : CoverFileType.values()) {
            if (coverFileType.getCode().equals(code)) {
                return coverFileType;
            }
        }
        return null;
    }

    public static CoverFileType getByDesc(String desc) {
        for (CoverFileType coverFileType : CoverFileType.values()) {
            if (coverFileType.getDesc().equals(desc)) {
                return coverFileType;
            }
        }
        return null;
    }
}
