package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/6 18:29
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum BusinessTaskType {
    UN_KNOW(-1, "未知"),

    PERIOD(1, "账期"),
    CUSTOMER(2, "服务"),

    ;

    private final Integer code;

    private final String name;

    public static BusinessTaskType getByCode(Integer source) {
        for (BusinessTaskType item : BusinessTaskType.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static Boolean isPeriod(Integer source) {
        return Objects.equals(source, PERIOD.code);
    }
}
