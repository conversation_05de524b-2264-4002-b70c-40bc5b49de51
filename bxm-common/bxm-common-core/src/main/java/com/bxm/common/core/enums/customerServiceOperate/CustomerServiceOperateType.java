package com.bxm.common.core.enums.customerServiceOperate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 12:28
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum CustomerServiceOperateType {
    END(1, "移出"),
    FROZE(2, "冻结"),
    UN_FROZE(3, "解冻"),
    RESTART(4, "重启"),
    ;

    private final Integer code;

    private final String name;

    private static final List<Integer> WORK_BENCH_CALC = Arrays.asList(END.getCode(), RESTART.getCode());

    public static List<Integer> workBenchCalc() {
        return WORK_BENCH_CALC;
    }
}
