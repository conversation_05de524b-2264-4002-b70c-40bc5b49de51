package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开关、是否、有无
 *
 * <AUTHOR>
 * @date 2024/7/5 17:45
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum YesNo {
    UN_KNOW(-1, "未知"),

    YES(1, "是"),
    NO(0, "否"),
    ;

    private final Integer code;

    private final String name;

    public static YesNo getByCode(Integer source) {
        for (YesNo item : YesNo.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
