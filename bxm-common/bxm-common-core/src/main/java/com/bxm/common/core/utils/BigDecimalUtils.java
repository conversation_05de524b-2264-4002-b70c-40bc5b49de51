package com.bxm.common.core.utils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/6 16:52
 * happy coding!
 */
public class BigDecimalUtils {
    public static BigDecimal add(BigDecimal... values) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal value : values) {
            result = result.add(defaultHandleNull(value));
        }
        return result;
    }

    public static BigDecimal defaultHandleNull(BigDecimal source) {
        return source == null ? BigDecimal.ZERO : source;
    }
}
