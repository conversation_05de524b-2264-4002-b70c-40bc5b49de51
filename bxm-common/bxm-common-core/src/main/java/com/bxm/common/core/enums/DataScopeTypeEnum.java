package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DataScopeTypeEnum {

    // 数据权限类型，1-总部，2-职能，3-工作台
    TOTAL(1, "总部"),
    FUNCTIONAL(2, "职能部门"),
    WORK_PLATFORM(3, "工作台"),
    ;

    private final Integer code;

    private final String name;

    public static String getNameByCode(Integer code) {
        for (DataScopeTypeEnum dataScopeTypeEnum : DataScopeTypeEnum.values()) {
            if (dataScopeTypeEnum.getCode().equals(code)) {
                return dataScopeTypeEnum.getName();
            }
        }
        return "";
    }
}
