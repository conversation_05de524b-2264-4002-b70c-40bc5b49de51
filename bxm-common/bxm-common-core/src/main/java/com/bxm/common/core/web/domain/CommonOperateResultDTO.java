package com.bxm.common.core.web.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class CommonOperateResultDTO {

    @ApiModelProperty("操作成功的列表")
    private List<CommonOperDTO> successList;

    @ApiModelProperty("操作失败的列表")
    private List<CommonOperDTO> failList;

    @ApiModelProperty("操作的列表")
    private List<CommonOperDTO> totalList;

    public CommonOperateResultDTO() {
        this.successList = new ArrayList<>();
        this.failList = new ArrayList<>();
        this.totalList = new ArrayList<>();
    }
}
