package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BankReceiptStatus {

    // 银行账号回单卡状态，1-已托管，2-未托管
    UN_TRUSTED(1, "已托管"),
    TRUSTED(2, "未托管"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static BankReceiptStatus getBankReceiptStatusByReceiptStatus(Integer receiptStatus) {
        for (BankReceiptStatus bankReceiptStatus : BankReceiptStatus.values()) {
            if (bankReceiptStatus.getCode().equals(receiptStatus)) {
                return bankReceiptStatus;
            }
        }
        return UN_KNOW;
    }
}
