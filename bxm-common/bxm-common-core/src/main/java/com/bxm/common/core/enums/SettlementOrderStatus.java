package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum SettlementOrderStatus {

    // 结算单状态，1-已创建待推送，2-结算中，3-已驳回，4-已撤回，5-已确认
    WAIT_PUSH(1, "已创建待推送"),
    SETTLEMENTING(2, "结算中"),
    REJECT(3, "已驳回"),
    REVOKE(4, "已撤回"),
    CONFIRM(5, "已确认"),

    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static SettlementOrderStatus getByCode(Integer code) {
        for (SettlementOrderStatus status : SettlementOrderStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_KNOW;
    }

    public static List<Integer> canDeleteStatus() {
        return Arrays.asList(WAIT_PUSH.getCode(), REJECT.getCode(), REVOKE.getCode());
    }
}
