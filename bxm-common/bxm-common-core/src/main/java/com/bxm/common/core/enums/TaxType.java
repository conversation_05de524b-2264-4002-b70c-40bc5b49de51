package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/6/12 21:05
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum TaxType {
    SMALL(1, "小规模"),
    COMMONLY(2, "一般纳税人"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;
    private final String desc;

    public static TaxType getByCode(Integer code) {
        for (TaxType value : TaxType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UN_KNOW;
    }
}
