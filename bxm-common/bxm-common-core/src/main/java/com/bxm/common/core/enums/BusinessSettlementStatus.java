package com.bxm.common.core.enums;

import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BusinessSettlementStatus {

    // 业务结算状态，1-不可结算，2-待结算，3-结算中，4-已结算,5-补差结算中，6-补差已结算
    UNABLE_SETTLEMENT(1, "不可结算", "不可结算"),
    WAIT_SETTLEMENT(2, "待结算", "待结算"),
    SETTLEMENTING(3, "结算中", "结算中"),
    SETTLED(4, "已结算", "已结算"),
    SUPPLEMENT_SETTLEMENTING(5, "补差结算中", "已结算"),
    SUPPLEMENT_SETTLED(6, "补差已结算", "已结算"),

    UN_KNOW(99, "未知", "未知"),
    ;

    private final Integer code;

    private final String name;

    private final String showName;

    public static BusinessSettlementStatus getByCode(Integer code) {
        for (BusinessSettlementStatus businessSettlementStatus : values()) {
            if (businessSettlementStatus.getCode().equals(code)) {
                return businessSettlementStatus;
            }
        }
        return UN_KNOW;
    }

    public static String getMultiNamesByCodes(String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }
        String[] split = codes.split(",");
        return Arrays.stream(split).map(row -> getByCode(Integer.parseInt(row)).getName()).collect(Collectors.joining(","));
    }
}
