package com.bxm.common.core.enums.docHandover;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1-已完整、2-缺但齐、3-有缺失待补充
 *
 * <AUTHOR>
 * @date 2024/7/5 17:49
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum WholeLevel {
    UN_KNOW(-1, "未知"),

    COMPLETED(1, "已完整"),
    LACK_AND_COMPLETED(2, "缺但齐"),
    LACK_AND_COMPLETED_NEED_ADD(3, "有缺失"),

    UN_SUBMIT_DATA(-2, "未提交材料"),//实际上不是它的状态，是个聚合状态，就在这边写一下了

    WAIT_CHECK(4, "待核验"),// 再加一个聚合状态

    SUBMIT_NO_DATA(5, "无材料"),// 提交的几个模块都是 无
    ;

    private final Integer code;

    private final String name;

    public static WholeLevel getByCode(Integer source) {
        for (WholeLevel item : WholeLevel.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
