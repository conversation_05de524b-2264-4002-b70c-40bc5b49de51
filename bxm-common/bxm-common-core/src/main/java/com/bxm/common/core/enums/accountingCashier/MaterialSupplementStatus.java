package com.bxm.common.core.enums.accountingCashier;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialSupplementStatus {

    // 材料补充状态，1-无需处理，2-待核对，3-已核对，99-未知
    UNKNOWN(99, "未知"),
    NO_NEED_TO_PROCESS(1, "无需处理"),
    TO_BE_CHECKED(2, "待核对"),
    ALREADY_CHECKED(3, "已核对"),
    ;

    private final Integer code;
    private final String desc;

    public static MaterialSupplementStatus getByCode(Integer code) {
        for (MaterialSupplementStatus materialSupplementStatus : MaterialSupplementStatus.values()) {
            if (materialSupplementStatus.getCode().equals(code)) {
                return materialSupplementStatus;
            }
        }
        return UNKNOWN;
    }
}
