package com.bxm.common.core.enums.repairAccount;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/27 23:30
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum RepairAccountDeliverStatus {
    UN_KNOW(-1, "未知"),

    NEED_DELIVER(1, "待交付"),
    ING(2, "交付中"),
    DONE(3, "已完成"),
    ;

    private final Integer code;

    private final String name;

    public static RepairAccountDeliverStatus getByCode(Integer source) {
        for (RepairAccountDeliverStatus item : RepairAccountDeliverStatus.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}

/*
 * 当补账还是待分派时，交付状态=待交付
 * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
 * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
 * 其他状态为交付中
 */

/*
 * 阿苏：
 * 提交：触发交付状态=待交付
 * 分派：轮一遍入账交付单，待交付/已完成
 * 入账交付单编辑保存：轮一遍入账交付单，待交付/交付中/已完成
 */
