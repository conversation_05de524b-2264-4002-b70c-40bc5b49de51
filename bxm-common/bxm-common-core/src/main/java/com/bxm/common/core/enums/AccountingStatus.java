package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccountingStatus {

    // 账务状态，1-正常，2-无需做账
    NORMAL(1, "正常"),

    NO_NEED(2, "无需做账"),

    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static AccountingStatus getByCode(Integer code) {
        for (AccountingStatus status : AccountingStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_KNOW;
    }
}
