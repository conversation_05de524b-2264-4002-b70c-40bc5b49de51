package com.bxm.common.core.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYYMM = "yyyyMM";

    public static String THIS_SEASON = "THIS_SEASON";

    public static String THIS_YEAR = "THIS_YEAR";

    public static String THIS_12_MONTH = "THIS_12_MONTH";

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static LocalDateTime getNowDate()
    {
        return LocalDateTime.now();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算时间差
     *
     * @param endDate 最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static String localDateToStr(LocalDate localDate, String pattern) {
        return localDate.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDate strToLocalDate(String dateStr, String pattern) {
        if (dateStr.length() == 6) {
            dateStr = dateStr + "01";
        }
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }

    public static Map<String, LocalDate> getStartEndByDateType(String dateType) {
        Map<String, LocalDate> result = new HashMap<>();
        LocalDate now = LocalDate.now();
        LocalDate start;
        LocalDate end;
        if (Objects.equals(dateType, THIS_SEASON)) {
            // 获取本季度的开始日期和结束日
            int monthValue = now.getMonthValue();
            if (monthValue <= 3) {
                start = LocalDate.of(now.getYear(), 1, 1);
                end = LocalDate.of(now.getYear(), 3, 31);
            } else if (monthValue <= 6) {
                start = LocalDate.of(now.getYear(), 4, 1);
                end = LocalDate.of(now.getYear(), 6, 30);
            } else if (monthValue <= 9) {
                start = LocalDate.of(now.getYear(), 7, 1);
                end = LocalDate.of(now.getYear(), 9, 30);
            } else {
                start = LocalDate.of(now.getYear(), 10, 1);
                end = LocalDate.of(now.getYear(), 12, 31);
            }
        } else if (Objects.equals(dateType, THIS_YEAR)) {
            start = LocalDate.of(now.getYear(), 1, 1);
            end = LocalDate.of(now.getYear(), 12, 31);
        } else {
            if (now.getMonthValue() == 12) {
                end = LocalDate.of(now.getYear(), now.getMonthValue(), 31);
            } else {
                end = LocalDate.of(now.getYear(), now.getMonthValue() + 1, 1).minusDays(1);
            }
            start = end.minusMonths(11).withDayOfMonth(1);
        }
        result.put("startDate", start);
        result.put("endDate", end);
        return result;
    }

    public static long localDateToSecond(LocalDateTime now) {
        return now.toEpochSecond(ZoneOffset.of("+8"));
    }

    public static long localDateToMillSecond(LocalDateTime now) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = now.atZone(zoneId);

        return zonedDateTime.toInstant().toEpochMilli();

    }

    public static Integer getNowPeriod() {
        return Integer.parseInt(localDateToStr(LocalDate.now(), YYYYMM));
    }

    public static Integer getDiffPeriod(Integer diff) {
        return Integer.parseInt(localDateToStr(LocalDate.now().plusMonths(diff), YYYYMM));
    }

    public static Integer getPrePeriod() {
        return Integer.parseInt(localDateToStr(LocalDate.now().minusMonths(1), YYYYMM));
    }

    public static LocalDateTime dateToLocaldateTime(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDate dateToLocaldate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Integer getNextPeriod() {
        return Integer.parseInt(localDateToStr(LocalDate.now().plusMonths(1), YYYYMM));
    }

    public static Integer getNextPeriod(Integer period) {
        int nextPeriod;
        if (period % 100 == 12) {
            nextPeriod = Integer.parseInt((period / 100 + 1) + "01");
        } else {
            nextPeriod = period + 1;
        }
        return nextPeriod;
    }

    public static String periodToYeaMonth(Integer period) {
        return period.toString().substring(0, 4) + "-" + period.toString().substring(4, 6);
    }

    public static Integer yearMonthToPeriod(String period) {
        return Integer.parseInt(period.replace("-", ""));
    }

    public static String getYearMonthName(Integer yearMonth) {
        return yearMonth % 100 + "月";
    }

    public static String getPreMonthStartTime() {
        LocalDate now = LocalDate.now();
        return now.minusMonths(1).withDayOfMonth(1).format(DateTimeFormatter.ofPattern(YYYY_MM_DD)) + " 00:00:00";
    }

    public static String getPreMonthEndTime() {
        LocalDate now = LocalDate.now();
        return now.minusMonths(1).withDayOfMonth(now.minusMonths(1).lengthOfMonth()).format(DateTimeFormatter.ofPattern(YYYY_MM_DD)) + " 23:59:59";
    }

    public static String getPreMonthStartTime(LocalDate date) {
        return date.withDayOfMonth(1).format(DateTimeFormatter.ofPattern(YYYY_MM_DD)) + " 00:00:00";
    }

    public static String getPreMonthEndTime(LocalDate date) {
        return date.withDayOfMonth(date.lengthOfMonth()).format(DateTimeFormatter.ofPattern(YYYY_MM_DD)) + " 23:59:59";
    }

    public static Integer getLastYear12Period() {
        LocalDate now = LocalDate.now();
        return Integer.parseInt((now.getYear() - 1) + "12");
    }

    public static Integer getPrePrePeriod() {
        return Integer.parseInt(localDateToStr(LocalDate.now().minusMonths(2), YYYYMM));
    }
}
