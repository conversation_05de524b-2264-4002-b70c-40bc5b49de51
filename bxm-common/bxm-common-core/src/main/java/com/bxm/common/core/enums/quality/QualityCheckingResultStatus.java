package com.bxm.common.core.enums.quality;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QualityCheckingResultStatus {

    // 质检结果状态，0-未执行，1-已执行，99-未知
    UNEXECUTED(0, "未执行"),
    EXECUTED(1, "已执行"),
    UNKNOWN(99, "未知");

    private final Integer code;

    private final String name;

    public static QualityCheckingResultStatus getByCode(Integer code) {
        for (QualityCheckingResultStatus value : QualityCheckingResultStatus.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
