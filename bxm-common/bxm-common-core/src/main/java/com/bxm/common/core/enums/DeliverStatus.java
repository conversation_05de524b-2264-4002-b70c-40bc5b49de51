package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DeliverStatus {

    STATUS_0(0, "已提交待申报", 6),
    STATUS_1(1, "申报已保存待提交", 6),
    STATUS_2(2, "已申报待确认", 0),
    STATUS_3(3, "已确认待扣款", 2),
    STATUS_4(4, "扣款已保存待提交", 2),
    STATUS_5(5, "已扣款", 3),
    STATUS_6(6, "待重提", null),
    STATUS_7(7, "申报异常", null),
    STATUS_8(8, "关闭申报", null),
    STATUS_9(9, "扣款异常", null),
    STATUS_10(10, "关闭扣款", null),
    STATUS_11(11, "待提交", null),
    STATUS_12(12, "已完成", 0),
    STATUS_101(101, "已提交待认证", 105),
    STATUS_102(102, "认证已保存待提交", 105),
    STATUS_103(103, "已认证待确认", 101),
    STATUS_104(104, "已确认待完结", null),
    STATUS_105(105, "待重提", null),
    STATUS_106(106, "认证异常", null),
    STATUS_107(107, "关闭认证", null),
    STATUS_108(108, "已完结", null),
    STATUS_109(109, "完结异常", null),
    STATUS_110(110, "关闭交付", null),
    UN_KNOW(999, "未知", null),
    ;

    private final Integer code;

    private final String name;

    // 执行退回后的状态
    private final Integer afterReBackStatus;

    public static DeliverStatus getDeliverStatusByCode(Integer deliverStatus) {
        for (DeliverStatus deliverStatusEnum : DeliverStatus.values()) {
            if (deliverStatusEnum.getCode().equals(deliverStatus)) {
                return deliverStatusEnum;
            }
        }
        return UN_KNOW;
    }

    public static List<Integer> getReBackStatus() {
        List<Integer> result = new ArrayList<>();
        result.add(STATUS_0.getCode());
        result.add(STATUS_1.getCode());
        result.add(STATUS_2.getCode());
        result.add(STATUS_3.getCode());
        result.add(STATUS_4.getCode());
        result.add(STATUS_5.getCode());
        result.add(STATUS_101.getCode());
        result.add(STATUS_102.getCode());
        result.add(STATUS_103.getCode());
        result.add(STATUS_104.getCode());
        return result;
    }

    public static List<Integer> getBatchReBackStatus() {
        List<Integer> result = new ArrayList<>();
        result.add(STATUS_2.getCode());
        result.add(STATUS_103.getCode());
        return result;
    }

    public static List<Integer> canSupplementStatus() {
        return Arrays.asList(STATUS_2.getCode(), STATUS_3.getCode(), STATUS_4.getCode(), STATUS_5.getCode());
    }

    public static List<Integer> canOpenApiReportStatus() {
        return Arrays.asList(STATUS_0.getCode(), STATUS_1.getCode(), STATUS_2.getCode(), STATUS_3.getCode(), STATUS_4.getCode(), STATUS_5.getCode());
    }

    public static List<Integer> canSubmitStatus() {
        return Arrays.asList(STATUS_1.getCode(), STATUS_4.getCode(), STATUS_6.getCode(), STATUS_11.getCode(), STATUS_102.getCode(), STATUS_105.getCode());
    }

     public static List<Integer> canReBackStatus() {
        return Arrays.stream(DeliverStatus.values()).filter(row -> !Objects.isNull(row.getAfterReBackStatus()))
                .map(DeliverStatus::getCode).collect(Collectors.toList());
    }
}
