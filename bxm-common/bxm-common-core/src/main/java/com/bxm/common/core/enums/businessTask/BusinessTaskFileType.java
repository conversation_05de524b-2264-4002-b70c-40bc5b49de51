package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/1 21:07
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum BusinessTaskFileType {
    UN_KNOW(-1, "未知"),

    BASE(1, "基础附件"),

    CLOSE(3, "关闭"),
    DELETE(4, "删除"),
    DISTRIBUTE(5, "分单"),
    ASSIGN(6, "派单"),
    FINISH(7, "完成"),
    CHECK(8, "审核"),
    HANDLE_EXCEPTION(9, "处理异常"),
    BANK_MATERIAL(10, "银行材料"),


    ;

    private final Integer code;

    private final String name;

    public static BusinessTaskFileType getByCode(Integer source) {
        for (BusinessTaskFileType item : BusinessTaskFileType.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
