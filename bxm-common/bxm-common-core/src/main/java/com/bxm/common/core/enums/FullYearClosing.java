package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FullYearClosing {

    // 全年结账，1-待完成，2-已完成
    WAIT_FINISH(1, "待完成"),
    FINISH(2, "已完成"),
    UN_KNOWN(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    public static FullYearClosing getByCode(Integer code) {
        for (FullYearClosing item : FullYearClosing.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOWN;
    }

    public static FullYearClosing getByDesc(String desc) {
        for (FullYearClosing item : FullYearClosing.values()) {
            if (item.getDesc().equals(desc)) {
                return item;
            }
        }
        return UN_KNOWN;
    }
}
