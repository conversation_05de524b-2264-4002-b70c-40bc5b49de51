package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GroupMap {

    FUZHOU("1492060944783888385", "福州融易算君道"),
    TAIJIANG("1495770080991608834", "台江融易算君道"),
    NINGDE("1495771212035334146", "融易算君道宁德"),
    J<PERSON><PERSON>("1495771848378359810", "晋安融易算君道"),
    CANGSHAN("1495772339904651266", "仓山融易算君道"),
    FUAN("1495773158230138881", "融易算君道福安"),
    FUDING("1495773507863805953", "融易算君道福鼎"),
    LONGYAN("1498120906013171713", "融易算君道龙岩"),
    ZHANGZHOU("1498121241255501826", "融易算君道漳州"),
    SANMING("1498121440840085506", "融易算君道三明"),
    XIAMEN("1498121610893946881", "融易算君道厦门"),
    KUAIXIAOGE("1498121925463924737", "融易算君道快小哥"),
    NINGDE_ERQU("1640615064409354242", "融易算宁德君道瞪羚"),
    CHANGLE("1693454122419163137", "融易算君道长乐"),
    QUANZHOU("1405092270095802370", "泉州融易算君道企业管理咨询有限公司"),
    GAOXINQU("1729318716725604353", "福州高新区"),
    SHICHANGBU_TEST("1499268894365020162", "融易算君道市场部"),
    ;

    private final String groupId;

    private final String groupName;

    public static GroupMap getByGroupId(String groupId) {
        for (GroupMap businessGroupMap : GroupMap.values()) {
            if (businessGroupMap.groupId.equals(groupId)) {
                return businessGroupMap;
            }
        }
        return null;
    }

    public static String getGroupNameByGroupId(String groupId) {
        for (GroupMap businessGroupMap : GroupMap.values()) {
            if (businessGroupMap.groupId.equals(groupId)) {
                return businessGroupMap.getGroupName();
            }
        }
        return "";
    }
}
