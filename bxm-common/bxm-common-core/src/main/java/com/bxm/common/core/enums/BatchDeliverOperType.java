package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BatchDeliverOperType {

    //1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常，9-补充申报附件
    NEW(1, "新建"),
    DECLARE(2, "申报"),
    REMOVE_DECLARE_EXCEPTION(3, "解除申报异常"),
    DEDUCTION(4, "扣款"),
    REMOVE_DEDUCTION_EXCEPTION(5, "解除扣款异常"),
    SUPPLEMENT(6, "补充"),
    AUTH(7, "认证"),
    REMOVE_AUTH_EXCEPTION(8, "解除认证异常"),
    SUPPLEMENT_REPORT_FILE(9, "补充申报附件"),
    IN_ACCOUNT_EDIT(10, "入账编辑"),
    CONFIRM(11, "确认"),
    IN_ACCOUNT_EDIT_V2(12, "入账编辑"),
    IN_ACCOUNT_RPA_UPDATE(13, "入账RPA更新"),
    IN_ACCOUNT_SETTLEMENT(14, "入账结算"),
    NEW_HOUSE_PREPAY_SETTLEMENT(15, "新户预收结算"),
    REJECT(16, "驳回"),
    FINISH(17, "完结"),
    REMOVE_FINISH_EXCEPTION(18, "解除完结异常"),
    INCOME_EDIT(19, "收入编辑"),
    YEAR_SUMMARY_EDIT(20, "年度汇总编辑"),
    UPDATE_TAX_REPORT_TOTAL_AMOUNT(21, "更新个税申报总额"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;
    private final String name;

    public static BatchDeliverOperType getByCode(Integer code) {
        for (BatchDeliverOperType operType : BatchDeliverOperType.values()) {
            if (operType.getCode().equals(code)) {
                return operType;
            }
        }
        return UN_KNOW;
    }
}
