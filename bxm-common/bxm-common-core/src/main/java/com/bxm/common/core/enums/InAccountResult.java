package com.bxm.common.core.enums;

import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum InAccountResult {

    // 1-正常2-异常3-无需交付4-无账务
    NORMAL(1, "正常"),
    EXCEPTION(2, "异常"),
    NO_DELIVER(3, "无需交付"),
    NO_ACCOUNT(4, "无账务"),
    UN_KNOWN(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    private static List<Integer> UN_NORMAL = Arrays.asList(NO_DELIVER.getCode(), EXCEPTION.getCode());

    public static InAccountResult getByCode(Integer code) {
        for (InAccountResult item : InAccountResult.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOWN;
    }

    public static List<Integer> setInTimeResult() {
        return Arrays.asList(
                NORMAL.getCode(),
                NO_DELIVER.getCode(),
                NO_ACCOUNT.getCode()
        );
    }

    public static InAccountResult getByName(String source) {
        for (InAccountResult item : InAccountResult.values()) {
            if (Objects.equals(source, item.getName())) {
                return item;
            }
        }
        return UN_KNOWN;
    }

    public static Boolean unNormal(Integer code) {
        return UN_NORMAL.contains(code);
    }

    public static String getMultiNamesByCodes(String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }
        String[] split = codes.split(",");
        return Arrays.stream(split).map(row -> getByCode(Integer.parseInt(row)).getName()).collect(Collectors.joining(","));
    }
}
