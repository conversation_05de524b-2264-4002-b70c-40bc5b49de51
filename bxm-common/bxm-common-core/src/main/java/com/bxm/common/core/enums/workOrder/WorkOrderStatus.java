package com.bxm.common.core.enums.workOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum WorkOrderStatus {

    // 工单状态，1-待完结，2-已完结，3-超时关闭，4-已完结待确认，5-超时确认，99-未知
    WAIT_END(1, "待完结"),
    ENDED(2, "已完结"),
    OVERTIME_CLOSE(3, "超时关闭"),
    ENDED_CONFIRM(4, "已完结待确认"),
    OVERTIME_CONFIRM(5, "超时确认"),
    UNKNOWN(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    public static WorkOrderStatus getByCode(Integer code) {
        for (WorkOrderStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    public static boolean canModifyDdl(Integer status) {
        return WAIT_END.getCode().equals(status) || ENDED_CONFIRM.getCode().equals(status);
    }

    public static List<Integer> getCanFollowUpStatus() {
        return Arrays.asList(WAIT_END.getCode());
    }

    public static List<Integer> getCanTransmitStatus() {
        return Arrays.asList(WAIT_END.getCode(), ENDED_CONFIRM.getCode());
    }

    public static List<Integer> getCanConfirmStatus() {
        return Arrays.asList(ENDED_CONFIRM.getCode());
    }

    public static List<Integer> getCanCommentStatus() {
        return Arrays.asList(WAIT_END.getCode(), ENDED_CONFIRM.getCode());
    }
}
