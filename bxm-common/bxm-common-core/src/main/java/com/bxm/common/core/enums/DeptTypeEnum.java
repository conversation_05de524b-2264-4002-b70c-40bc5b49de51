package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeptTypeEnum {

    // 组织类型，1-业务公司，2-会计工厂
    BUSINESS_COMPANY(1, "业务公司"),
    ACCOUNTING_FACTORY(2, "会计工厂"),
    ;

    private final Integer code;

    private final String name;

    public static String getNameByCode(Integer code) {
        for (DeptTypeEnum deptTypeEnum : DeptTypeEnum.values()) {
            if (deptTypeEnum.getCode().equals(code)) {
                return deptTypeEnum.getName();
            }
        }
        return "";
    }
}
