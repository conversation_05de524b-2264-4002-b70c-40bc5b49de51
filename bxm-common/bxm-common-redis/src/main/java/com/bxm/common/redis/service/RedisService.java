package com.bxm.common.redis.service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

/**
 * spring redis 工具类
 * 
 * <AUTHOR>
 **/
@SuppressWarnings(value = { "unchecked", "rawtypes" })
@Component
public class RedisService
{
    @Autowired
    public RedisTemplate redisTemplate;

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value)
    {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit)
    {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout)
    {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit)
    {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key)
    {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key)
    {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key)
    {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key)
    {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection)
    {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key 缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList)
    {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key)
    {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    public <T> void setLargeCacheList(final String keyPrefix, final List<T> dataList, final int batchSize, final long timeout, final TimeUnit timeUnit) {
        int totalSize = dataList.size();
        int numberOfBatches = (int) Math.ceil((double) totalSize / batchSize);

        for (int i = 0; i < numberOfBatches; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, totalSize);

            List<T> batchList = dataList.subList(startIndex, endIndex);
            String key = keyPrefix + ":" + i;

            redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                redisTemplate.opsForList().rightPushAll(key, batchList.toArray());
                redisTemplate.expire(key, timeout, timeUnit);  // 设置过期时间
                return null;
            });
        }
    }

    public void deleteLargeCacheList(final String keyPrefix) {
        int batchIndex = 0;
        List<String> keysToDelete = new ArrayList<>();

        while (true) {
            String key = keyPrefix + ":" + batchIndex;
            Boolean exists = redisTemplate.hasKey(key);

            // 如果键不存在，跳出循环
            if (exists == null || !exists) {
                break;
            }

            keysToDelete.add(key);
            batchIndex++;
        }

        if (!keysToDelete.isEmpty()) {
            // 使用Redis的delete方法批量删除
            redisTemplate.delete(keysToDelete);
        }
    }

    public <T> List<T> getLargeCacheList(final String keyPrefix, final int batchSize) {
        List<T> resultList = new ArrayList<>();

        int i = 0;
        while (true) {
            String key = keyPrefix + ":" + i;
            List<T> batchList = redisTemplate.opsForList().range(key, 0, batchSize - 1);

            if (batchList == null || batchList.isEmpty()) {
                break;
            }

            resultList.addAll(batchList);

            // 如果实际取到的记录数小于 batchSize，说明已经读完了
            if (batchList.size() < batchSize) {
                break;
            }

            i++;
        }

        return resultList;
    }


    /**
     * 缓存Set
     *
     * @param key 缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet)
    {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext())
        {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key)
    {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap)
    {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key)
    {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value)
    {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey)
    {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys)
    {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey)
    {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    public Long increment(String key) {
        // 使用默认的缓存时间（-1表示永不过期）
        return redisTemplate.opsForValue().increment(key);
    }

    public Long incrementWithInitialValue(String key, long initialValue) {
        // 如果key不存在，从初始值开始
        return redisTemplate.opsForValue().increment(key, initialValue);
    }

    public Long incrementWithTimeToLive(String key, long timeToLive, TimeUnit timeUnit) {
        // 同时设置自增操作后的TTL（生存时间）
        Long increment = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, timeToLive, timeUnit);
        return increment;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern)
    {
        return redisTemplate.keys(pattern);
    }

    private static final Long RELEASE_SUCCESS = 1L;
    private static final Long POSTPONE_SUCCESS = 1L;
    private static final int MAX_TRY = 5;
    // 解锁脚本(lua)
    private static final String RELEASE_LOCK_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
    // 延时脚本
    private static final String POSTPONE_LOCK_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('expire', KEYS[1], ARGV[2]) else return '0' end";

    /**
     * 获取锁
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public boolean lock(String key, String value, long expireTime) {
        return lock(key, value, expireTime, 0);
    }

    /**
     * 获取锁
     * @param key
     * @param value
     * @param expireTime
     * @param count
     * @return
     */
    private Boolean lock(String key, String value, long expireTime, int count) {
        // 加锁
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, TimeUnit.SECONDS);
        if (!lock && count < MAX_TRY) { // 加锁失败，尝试重新获取
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return lock(key, value, expireTime, ++count);
        } else {  // 加锁成功, 启动一个守护线程, 防止业务逻辑未执行完毕就因锁超时而使锁释放
            PostponeDaemonTask daemonTask = new PostponeDaemonTask(key, value, expireTime, true, this);
            Thread thread = new Thread(daemonTask);
            thread.setDaemon(true);
            thread.start();
        }
        return lock;
    }

    public Boolean lockNotWait(String key, String value, long expireTime) {
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, TimeUnit.SECONDS);
        if (!lock) {
            return false;
        }
        PostponeDaemonTask daemonTask = new PostponeDaemonTask(key, value, expireTime, true, this);
        Thread thread = new Thread(daemonTask);
        thread.setDaemon(true);
        thread.start();
        return true;
    }

    /**
     * 释放锁
     * @param key
     * @param value
     * @return
     */
    public Boolean unlock(String key, String value) {
        Long result = (Long) redisTemplate.execute(new DefaultRedisScript<Long>(RELEASE_LOCK_SCRIPT, Long.class), Arrays.asList(key), value);
        if (RELEASE_SUCCESS.equals(result)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 延时锁
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public Boolean postpone(String key, String value, long expireTime) {
        Long result = (Long) redisTemplate.execute(new DefaultRedisScript<Long>(POSTPONE_LOCK_SCRIPT, Long.class), Arrays.asList(key), Arrays.asList(value, String.valueOf(expireTime)));
        if (POSTPONE_SUCCESS.equals(result)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
