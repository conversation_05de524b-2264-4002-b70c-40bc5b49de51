package com.bxm.common.log.service;

import com.bxm.common.core.domain.R;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.system.api.RemoteLogService;
import com.bxm.system.api.domain.SysOperLog;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;

/**
 * 异步调用日志服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncLogService
{
    @Autowired
    private RemoteLogService remoteLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    /**
     * 保存系统日志记录
     */
    @Async
    public void saveSysLog(SysOperLog sysOperLog) throws Exception
    {
        remoteLogService.saveLog(sysOperLog, SecurityConstants.INNER);
    }

    /**
     * 保存业务日志
     */
    public void saveBusinessLog(BusinessLogDTO dto)
    {
        if (StringUtils.isEmpty(dto.getOperName()) && !Objects.isNull(dto.getDeptId()) && !Objects.isNull(dto.getOperUserId())) {
            R<List<SysEmployee>> resp = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(dto.getDeptId(), dto.getOperUserId());
            if (!Objects.isNull(resp) && !ObjectUtils.isEmpty(resp.getData())) {
                dto.setOperName(resp.getData().get(0).getEmployeeName());
                if (StringUtils.isEmpty(dto.getDeptName())) {
                    R<SysDept> deptInfo = remoteDeptService.getDeptInfo(resp.getData().get(0).getDeptId());
                    if (!Objects.isNull(deptInfo) && !Objects.isNull(deptInfo.getData())) {
                        dto.setDeptName(deptInfo.getData().getDeptName());
                    }
                }
            }
        }
        remoteLogService.saveBusinessLog(dto);
    }

    public void saveBatchBusinessLog(List<BusinessLogDTO> dtoList)
    {
        if (!ObjectUtils.isEmpty(dtoList)) {
            remoteLogService.batchAddLog(dtoList);
        }
    }

//    /**
//     * 保存业务日志
//     */
//    @Async
//    public void saveBusinessLogV2(BusinessLogDTO dto)
//    {
//        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
//        Long userId = SecurityUtils.getUserId();
//        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
//        dto.setDeptId(deptId);
//        dto.setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
//        dto.setOperUserId(userId);
//        if (StringUtils.isEmpty(dto.getOperName()) && !Objects.isNull(dto.getDeptId()) && !Objects.isNull(dto.getOperUserId())) {
//            R<List<SysEmployee>> resp = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(dto.getDeptId(), dto.getOperUserId());
//            if (!Objects.isNull(resp) && !ObjectUtils.isEmpty(resp.getData())) {
//                dto.setOperName(resp.getData().get(0).getEmployeeName());
//                if (StringUtils.isEmpty(dto.getDeptName())) {
//                    R<SysDept> deptInfo = remoteDeptService.getDeptInfo(resp.getData().get(0).getDeptId());
//                    if (!Objects.isNull(deptInfo) && !Objects.isNull(deptInfo.getData())) {
//                        dto.setDeptName(deptInfo.getData().getDeptName());
//                    }
//                }
//            }
//        }
//        remoteLogService.saveBusinessLog(dto);
//    }
}
