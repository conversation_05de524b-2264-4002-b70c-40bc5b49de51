package com.bxm.common.log.enums;

/**
 * 业务操作类型
 * 
 * <AUTHOR>
 */
public enum BusinessType
{
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,

    /**
     * 清空数据
     */
    CLEAN,

    /**
     * 结束服务
     */
    END,

    /**
     * 冻结服务
     */
    FROZE,

    /**
     * 解冻服务
     */
    UNFROZE,

    /**
     * 客户移组
     */
    CHANGE_BUSINESS_DEPT,

    /**
     * 客户换区
     */
    CHANGE_ACCOUNTING_TOP_DEPT,

    /**
     * 分派顾问
     */
    DISPATCH_ADVISOR,

    /**
     * 分派会计
     */
    DISPATCH_ACCOUNTING,

    /**
     * 无需分派
     */
    NOT_DISPATCH,

    /**
     * 重启服务
     */
    RESTART,

    /**
     * 更新工商信息
     */
    UPDATE_BUSINESS_INFORMATION,

    /**
     * 发送企微通知
     */
    COMPANY_WECHAT_SEND_MESSAGE,
}
