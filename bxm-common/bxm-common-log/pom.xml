<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bxm</groupId>
        <artifactId>bxm-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>bxm-common-log</artifactId>
	<version>${bxm.common.log.version}</version>
    <description>
        bxm-common-log日志记录
    </description>

    <dependencies>
		
        <!-- RuoYi Common Security -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-security</artifactId>
            <version>${bxm.common.security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-api-system</artifactId>
            <version>${bxm.api.system.version}</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://*************:18081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://*************:18081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>