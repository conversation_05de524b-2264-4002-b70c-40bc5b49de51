package com.bxm.gateway.controller;

import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.gateway.service.ValidateCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 * 
 * <AUTHOR>
 */
@RestController
public class CaptchaController
{

    @Autowired
    private ValidateCodeService validateCodeService;
    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode() throws IOException
    {
        return validateCodeService.createCaptcha();
    }
}
