spring:
  application:
    # 应用名称
    name: bxm-gateway
  profiles:
    active: dev
  cloud:
    gateway:
      routes:
        - id: bxm-auth
          uri: lb://bxm-auth
          predicates:
            - Path=/bxmAuth/**

        - id: bxm-file
          uri: lb://bxm-file
          predicates:
            - Path=/bxmFile/**

        - id: bxm-gen
          uri: lb://bxm-gen
          predicates:
            - Path=/bxmGen/**

        - id: bxm-job
          uri: lb://bxm-job
          predicates:
            - Path=/bxmJob/**

        - id: bxm-system
          uri: lb://bxm-system
          predicates:
            - Path=/bxmSystem/**

        - id: bxm-customer
          uri: lb://bxm-customer
          predicates:
            - Path=/bxmCustomer/**

        - id: bxm-thirdpart
          uri: lb://bxm-thirdpart
          predicates:
            - Path=/bxmThirdpart/**

        - id: bxm-openapi
          uri: lb://bxm-openapi
          predicates:
            - Path=/bxmOpenApi/**